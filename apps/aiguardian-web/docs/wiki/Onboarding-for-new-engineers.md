
## Set up
* [Create SSH keys for Github](https://dev.to/starkydevs/how-to-set-up-ssh-keys-for-github-a-step-by-step-guide-55p0)
* Check out monorepo: `<NAME_EMAIL>:dsaidgovsg/aiguardian-monorepo.git`

* For Unix systems, run `corepack enable` to use Corepack to manage pnpm
* Install [pnpm](https://pnpm.io/installation), [turborepo](https://turbo.build/repo/docs/getting-started/installation), [poetry](https://python-poetry.org/docs/#installation), [pre-commit](https://pre-commit.com/#install) (more info: [Onboarding ‐ Typescript](Onboarding-‐-Typescript) and [Onboarding ‐ Python](Onboarding-‐-Python)) 

* Check out submodules: `pnpm git:init-submodules` (more info: [Submodules](Submodules))
* Run `pnpm install`
* Start database and localstack (AWS) `docker compose up  -d database localstack-with-setup`

## Prep environment
* Copy `.env.template` to `.env` in these folders or use this script in Mac/Unix:
```
cp apps/litmus-web/.env.template apps/litmus-web/.env
cp apps/moonshot/.env.template apps/moonshot/.env
cp apps/sentinel-api/.env.template apps/sentinel-api/.env
cp apps/sample-web/.env.template apps/sample-web/.env
cp packages/litmus-db/.env.template packages/litmus-db/.env
cp packages/uam-db/.env.template packages/uam-db/.env
cp packages/sentinel-db/.env.template packages/sentinel-db/.env
```

* Run `pnpm db:seed` to seed data into database

## Start Litmus & Moonshot
* Run `pnpm dev --filter=@aiguardian/litmus-web --filter=@aiverify/moonshot`
* Open Litmus: `http://localhost:3000` and login using credentials (to be shared)
* Download Postman collection (link to be provided)

## Additional Suggestions
* Use one of these SQL editors: https://dbeaver.io/ or https://www.beekeeperstudio.io/
