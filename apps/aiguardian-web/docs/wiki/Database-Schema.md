## User Access Management Database

<details>
<summary>Requirements</summary>

The User Access Management (UAM) database schema supports:

- List of entities supporting AA:
    - tenants:
       - Examples:
         - GovText (GovTech): https://www.govtext.gov.sg/
         - AIBots (GovTech): https://www.govtext.gov.sg/
         - SmartCompose (GovTech): https://www.smartcompose.gov.sg/
         - VICA (GovTech): https://www.vica.gov.sg/
         - LTA: https://www.lta.gov.sg/

    - products:
         - GovText Tenant:
            - RAGaaS
            - LLMaaS
            - Transcribe: https://www.transcribe.gov.sg/
         - AIBots Tenant:
            - AIBots
         - SmartCompose Tenant:
            - SmartCompose
         - VICA Tenant:
            - VICA
         - LTA Tenant:
            - ...

    - users
    - roles
- A list of tenant roles and responsibilities:
    - tenant_superadmin (manage users + tenant_admin)
    - tenant_admin (manage products, endpoints, litmus/sentinel api keys)
    - litmus_executor
    - litmus_viewer
    - sentinel_tester
    - sentinel_viewer
- tenant super admins can add/remove users
- tenant admins can add/remove api keys for litmus & sentinel, add / edit / remove products in litmus, add / edit / remove endpoints in litmus
- tenant users can have one or more of these roles: litmus_executor, litmus_viewer, sentinel_tester, sentinel_viewer
- litmus_viewer role can view the dashboard analytics as well as past security test runs, and configuration data
- litmus_executor role can additionally run ad hoc security test suites, sentinel_tester role can access playground to run ad hoc guardrail tests
- test suites and tests are predefined
- past test runs are visible only to each tenant
- tests can be run against endpoints
- each tenant can have multiple products
- each product can have multiple endpoints
- some users can have access across multiple tenants with different roles
- Note: users authentication is provided by a separate OIDC system
</details>

[Database Schema - UAM](Database-Schema-‐-UAM)



### Litmus Database
Note: Tenants/Products/Endpoints are synced with UAM DB

[Database Schema - Litmus](Database-Schema-‐-Litmus)

## Sentinel Database
[Database Schema - Sentinel](Database-Schema-‐-Sentinel)


