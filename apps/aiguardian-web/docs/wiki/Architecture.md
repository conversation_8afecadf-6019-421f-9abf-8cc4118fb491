Here's our temporary Architecture diagram. A proper one using draw.io will be added later.

Everything from ALB to the pods are in our [CStack](https://docs.developer.tech.gov.sg/docs?product=Container%20Stack%20(CStack)) EKS Cluster.

![image](uploads/ec47e1b029a7a96c6dd60b04a675d1a3/image.png)

```mermaid
graph TD
	public --> |WAF| cloudfront_sentinel --> |API Key| api_gateway_sentinel --> |API Key| public_alb[[public_alb]] --> sentinel_pods
	
	sentinel_pods --> RDS
	
	public --> |WAF| cloudfront_litmus --> |API Key| api_gateway_litmus --> |API Key| public_alb --> litmus_pods
	
	litmus_pods --> moonshot_pods
  moonshot_pods --> models_pods
  sentinel_pods --> models_pods
	
	litmus_pods --> RDS[(RDS)]
	litmus_pods --> EFS{{EFS}}
	
	moonshot_pods --> EFS
	models_pods --> EFS
	models_pods --> RDS
	
	cloudfront_litmus --> |web - JWT| public_alb
	
	public --> |WAF| cloudfront_website --> |origin-access-control| aig_website_S3
```
