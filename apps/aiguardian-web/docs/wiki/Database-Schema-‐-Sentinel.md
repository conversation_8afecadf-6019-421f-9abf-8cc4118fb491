# AIG Sentinel database models
> Generated by [`prisma-markdown`](https://github.com/samchon/prisma-markdown)

- [default](#default)

## default
```mermaid
erDiagram
"guardrails" {
  String id PK
  String name UK
  String description
  String type
  Json default_config "nullable"
  DateTime created_at
  DateTime updated_at
}
"guardrail_configurations" {
  String id PK
  String name UK
  String tenant_id
  String guardrail_id FK
  Json configuration
  Boolean is_active
  DateTime created_at
  DateTime updated_at
}
"guardrail_checks" {
  String id PK
  String tenant_id
  String request_id
  GuardrailCheckType check_type
  Json check_inputs
  Json params "nullable"
  DateTime created_at
  DateTime updated_at
}
"guardrail_check_detailed_results" {
  String id PK
  String check_id FK
  String guardrail_id FK
  Json check_outputs
  Float score "nullable"
  String status "nullable"
  DateTime start_time
  DateTime end_time "nullable"
  Float duration
  Json additional_data "nullable"
  DateTime created_at
  DateTime updated_at
}
"guardrail_configurations" }o--|| "guardrails" : guardrail
"guardrail_check_detailed_results" }o--|| "guardrail_checks" : check
"guardrail_check_detailed_results" }o--|| "guardrails" : guardrail
```

### `guardrails`

**Properties**
  - `id`: 
  - `name`: 
  - `description`: 
  - `type`: 
  - `default_config`: 
  - `created_at`: 
  - `updated_at`: 

### `guardrail_configurations`

**Properties**
  - `id`: 
  - `name`: 
  - `tenant_id`: 
  - `guardrail_id`: 
  - `configuration`: 
  - `is_active`: 
  - `created_at`: 
  - `updated_at`: 

### `guardrail_checks`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `request_id`: 
  - `check_type`: 
  - `check_inputs`: 
  - `params`: 
  - `created_at`: 
  - `updated_at`: 

### `guardrail_check_detailed_results`

**Properties**
  - `id`: 
  - `check_id`: 
  - `guardrail_id`: 
  - `check_outputs`: 
  - `score`: 
  - `status`: 
  - `start_time`: 
  - `end_time`: 
  - `duration`: 
  - `additional_data`: 
  - `created_at`: 
  - `updated_at`: 