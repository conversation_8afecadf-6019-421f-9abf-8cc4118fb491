# Permission Matrix

## Roles
- **tenant_superadmin**: Manages users and assigns roles, including tenant_admin.
- **tenant_admin**: Manages products, endpoints, and API keys for Litmus & Sentinel.
- **litmus_executor**: Runs ad hoc security test suites in Litmus.
- **litmus_viewer**: Views dashboard analytics, past security test runs, and configuration data.
- **sentinel_tester**: Runs ad hoc guardrail tests in Sentinel.
- **sentinel_viewer**: Views Sentinel test results.

We also reserve these roles for AIGuardian team:
- **aig_superadmin**: Manage all tenants' users, including superadmins and admins.
- **aig_admin**: Manage all tenants' non-admin users.
- **aig_viewer**: Views all tenants' dashboard analytics.

## Permissions Table

| Permission/Action                          | tenant superadmin | tenant admin | litmus executor | litmus viewer | sentinel tester | sentinel viewer | aig superadmin | aig admin | aig viewer |
| ------------------------------------------ | ----------------- | ------------ | --------------- | ------------- | --------------- | --------------- | -------------- | --------- | ---------- |
| **Tenant Management**                      |                   |              |                 |               |                 |                 |                |           |            |
| View Tenants                               | ✅(own)            | ✅(own)       | ✅(own)          | ✅(own)        | ✅(own)          | ✅(own)          | ✅              | ✅         | ✅          |
| Add/Update/Delete Tenants                  | ❌                 | ❌            | ❌               | ❌             | ❌               | ❌               | ✅              | ❌         | ❌          |
| **User Management**                        |
| View/Add/Review Tenant SuperAdmin/Admin    | ❌                 | ❌            | ❌               | ❌             | ❌               | ❌               | ✅              | ❌         | ❌          |
| View/Add/Remove Tenant Admins              | ✅(own)            | ❌            | ❌               | ❌             | ❌               | ❌               | ✅              | ❌         | ❌          |
| View/Add/Remove Tenant User Roles          | ✅(own)            | ✅(own)       | ❌               | ❌             | ❌               | ❌               | ✅              | ✅         | ❌          |
| **Product Management (Litmus)**            |
| View/Add/Edit/Remove Products              | ❌                 | ✅(own)       | ❌               | ❌             | ❌               | ❌               | ✅              | ✅         | ❌          |
| **Endpoint Management (Litmus)**           |
| View/Add/Edit/Remove Endpoints             | ❌                 | ✅(own)       | ❌               | ❌             | ❌               | ❌               | ✅              | ✅         | ❌          |
| **API Key Management (Litmus & Sentinel)** |
| View/Add/Remove API Keys                   | ❌                 | ✅(own)       | ❌               | ❌             | ❌               | ❌               | ✅              | ✅         | ❌          |
| **Litmus Platform Access**                 |
| View Dashboard Analytics                   | ❌                 | ❌            | ❌               | ❌             | ❌               | ❌               | ✅              | ✅         | ✅          |
| View Product & Endpoint Details            | ❌                 | ❌            | ✅ (own)         | ✅ (own)       | ❌               | ❌               | ✅              | ✅         | ✅          |
| View Test Runs                             | ❌                 | ❌            | ✅ (own)         | ✅ (own)       | ❌               | ❌               | ✅              | ✅         | ✅          |
| Run Ad Hoc Security Test Suites            | ❌                 | ❌            | ✅ (own)         | ❌             | ❌               | ❌               | ✅              | ✅         | ❌          |
| **Sentinel Platform Access**               |
| View Guardrail Test Results                | ❌                 | ❌            | ❌               | ❌             | ✅ (own)         | ✅ (own)         | ✅              | ✅         | ✅          |
| Run Ad Hoc Guardrail Tests                 | ❌                 | ❌            | ❌               | ❌             | ✅ (own)         | ❌               | ✅              | ✅         | ❌          |


## Permission Details

### Tenant Management (TM)

| **Permission String** | **Short String** | **Description**                            |
| --------------------- | ---------------- | ------------------------------------------ |
| `tm_tenants_create`   | `tm_t_c`         | Grants ability to create (add) tenants.    |
| `tm_tenants_read`     | `tm_t_r`         | Grants ability to read (view) tenants.     |
| `tm_tenants_update`   | `tm_t_u`         | Grants ability to update tenants.          |
| `tm_tenants_delete`   | `tm_t_d`         | Grants ability to delete (remove) tenants. |

### User Management (UM)

| Permission String           | Short String | Description                                           |
| --------------------------- | ------------ | ----------------------------------------------------- |
| `um_superadmins_create`     | `um_sa_c`    | Grants ability to create (add) tenant superadmins.    |
| `um_superadmins_read`       | `um_sa_r`    | Grants ability to read (view) tenant superadmins.     |
| `um_superadmins_update`     | `um_sa_u`    | Grants ability to update tenant superadmins.          |
| `um_superadmins_delete`     | `um_sa_d`    | Grants ability to delete (remove) tenant superadmins. |
| `um_tenantadmins_create`    | `um_ta_c`    | Grants ability to create (add) tenant admins.         |
| `um_tenantadmins_read`      | `um_ta_r`    | Grants ability to read (view) tenant admins.          |
| `um_tenantadmins_update`    | `um_ta_u`    | Grants ability to update tenant admins.               |
| `um_tenantadmins_delete`    | `um_ta_d`    | Grants ability to delete (remove) tenant admins.      |
| `um_tenantuserroles_create` | `um_tur_c`   | Grants ability to create (add) tenant user roles.     |
| `um_tenantuserroles_read`   | `um_tur_r`   | Grants ability to read (view) tenant user roles.      |
| `um_tenantuserroles_update` | `um_tur_u`   | Grants ability to update tenant user roles.           |
| `um_tenantuserroles_delete` | `um_tur_d`   | Grants ability to delete (remove) tenant user roles.  |

### Litmus Dashboard Access (LDA)

| Permission String    | Short String | Description                                                      |
| -------------------- | ------------ | ---------------------------------------------------------------- |
| `lda_dashboard_read` | `lda_d_r`    | Grants ability to read (view) dashboard analytics within Litmus. |



### Litmus Product Management (LPM)

| Permission String     | Short String | Description                                               |
| --------------------- | ------------ | --------------------------------------------------------- |
| `lpm_products_create` | `lpm_p_c`    | Grants ability to create (add) products within Litmus.    |
| `lpm_products_read`   | `lpm_p_r`    | Grants ability to read (view) products within Litmus.     |
| `lpm_products_update` | `lpm_p_u`    | Grants ability to update products within Litmus.          |
| `lpm_products_delete` | `lpm_p_d`    | Grants ability to delete (remove) products within Litmus. |

### Litmus Endpoint Management (LEM)

| Permission String      | Short String | Description                                                |
| ---------------------- | ------------ | ---------------------------------------------------------- |
| `lem_endpoints_create` | `lem_e_c`    | Grants ability to create (add) endpoints within Litmus.    |
| `lem_endpoints_read`   | `lem_e_r`    | Grants ability to read (view) endpoints within Litmus.     |
| `lem_endpoints_update` | `lem_e_u`    | Grants ability to update endpoints within Litmus.          |
| `lem_endpoints_delete` | `lem_e_d`    | Grants ability to delete (remove) endpoints within Litmus. |


### Litmus Test Management (LTM)

| Permission String  | Short String | Description                                            |
| ------------------ | ------------ | ------------------------------------------------------ |
| `ltm_tests_create` | `ltm_t_c`    | Grants ability to create (add) tests within Litmus.    |
| `ltm_tests_read`   | `ltm_t_r`    | Grants ability to read (view) tests within Litmus.     |
| `ltm_tests_update` | `ltm_t_u`    | Grants ability to update tests within Litmus.          |
| `ltm_tests_delete` | `ltm_t_d`    | Grants ability to delete (remove) tests within Litmus. |


### API Key Management (AKM)

| **Permission String** | **Short String** | **Description**                                                   |
| --------------------- | ---------------- | ----------------------------------------------------------------- |
| `akm_apikeys_create`  | `akm_ak_c`       | Grants ability to create (add) API keys within Litmus & Sentinel. |
| `akm_apikeys_read`    | `akm_ak_r`       | Grants ability to read (view) API keys within Litmus & Sentinel.  |
| `akm_apikeys_update`  | `akm_ak_u`       | Grants ability to update API keys                                 |


### Sentinel Platform Access (SPA)

| **Permission String**       | **Short String** | **Description**                                               |
| --------------------------- | ---------------- | ------------------------------------------------------------- |
| `spa_guardrail_test_read`   | `spa_gr_r`       | Grants ability to read (view) guardrail test within Sentinel. |
| `spa_guardrail_test_update` | `spa_gr_u`       | Grants ability to update guardrail tests within Sentinel.     |




## Role Details

This section provides detailed information about each role in the system, including their official names, short names for reference, and comprehensive descriptions of their responsibilities and access levels.

| Role Name           | Short String | Description                                                                |
| ------------------- | ------------ | -------------------------------------------------------------------------- |
| `tenant_superadmin` | `ten_sa`     | Manages users and assigns roles, including tenant_admin                    |
| `tenant_admin`      | `ten_ad`     | Manages products, endpoints, and API keys for Litmus & Sentinel            |
| `litmus_executor`   | `lit_ex`     | Runs ad hoc security test suites in Litmus                                 |
| `litmus_viewer`     | `lit_vi`     | Views dashboard analytics, past security test runs, and configuration data |
| `sentinel_tester`   | `sen_te`     | Runs ad hoc guardrail tests in Sentinel                                    |
| `sentinel_viewer`   | `sen_vi`     | Views Sentinel test results                                                |
| `aig_superadmin`    | `aig_sa`     | Manage all tenants' users, including superadmins and admins                |
| `aig_admin`         | `aig_ad`     | Manage all tenants' non-admin users                                        |
| `aig_viewer`        | `aig_vi`     | Views all tenants' data and activities                                     |

## References