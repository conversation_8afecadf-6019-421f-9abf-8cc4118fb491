## Setup

- Checkout submodules: `pnpm git:init-submodules` or `pnpm git:update-submodules`

- Optional: Install Python versions
  - Install [pyenv](https://github.com/pyenv/pyenv?tab=readme-ov-file#installation) to manage Python versions.
  - Follow all the installation steps for pyenv, including [shell setup](https://github.com/pyenv/pyenv?tab=readme-ov-file#b-set-up-your-shell-environment-for-pyenv)
  - Install Python versions (we still use 3.11 for now but will remove in future):
    - For Mac:
      - using Homebrew : `brew install python@3.11` and `brew install python@3.12`
      - add Homebrew's Python to pyenv: `brew pyenv-sync`
    - For Windows:
      - using pyenv: `pyenv install 3.11` and `pyenv install 3.12`
  - Set global Python version: `pyenv global 3.12`

- Install [poetry](https://python-poetry.org/docs/#installation)
  - Install `pipx`: `brew install pipx`
  - Install `poetry`: `pipx install poetry`

- Add poetry plugins for poetry v2:
```shell
poetry self add poetry-plugin-shell
poetry self add poetry-plugin-export
```

- Run `pnpm install` (or `pnpm i`)

- Option 1 - From the repo root directory:
  - Run `pnpm dev --filter=@aiguardian/sentinel-api` to start the app

- Option 2 - From the app directory (e.g. `apps/sentinel-api`):
  - Run `poetry shell` (optional if you want to do `pip list` or `python src/some_file.py`)
  - Run `pnpm dev`


#### Additional configurations for SEEDed machines

```shell
mkdir -p "${HOME}/.config/.cloudflare"
curl -sSLj -o "${HOME}/.config/.cloudflare/Cloudflare_CA.pem" "https://seed-general-public-files.s3.ap-southeast-1.amazonaws.com/seed-cloudflare-root-certs/Cloudflare_CA.pem"
cat "${HOME}/.config/.cloudflare/Cloudflare_CA.pem" >> $(poetry run python -m certifi)`
```

Other Cloudflare related isuse: https://docs.developer.tech.gov.sg/docs/security-suite-for-engineering-endpoint-devices/support/configuration-of-common-developer-cli-tools-with-cloudflare-warp-guide


## Local Development

### Normal Dev

1. Run `docker compose up database` to start the Postgres database locally
2. Run `turbo run db:seed --filter=@aiguardian/uam-db` to add initial data to uam-db.
3. Run `turbo run db:seed --filter=@aiguardian/sentinel-db` to add initial data to sentinel-db.
4. Run `turbo run dev --filter=@aiguardian/sentinel-api` to start the Sentinel api server.
5. Use Postman to test the API

### Docker

1. Run `docker compose up sentinel-api database` to start the following services:

- `sentinel-api` - The Sentinel API server
- `database` - PostgreSQL database
2. Run `turbo run db:seed --filter=@aiguardian/uam-db` to add initial data to uam-db.
3. Run `turbo run db:seed --filter=@aiguardian/sentinel-db` to add initial data to sentinel-db.


## Useful links

- TBA


