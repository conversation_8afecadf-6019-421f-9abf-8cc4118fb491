# AIG Litmus database models
> Generated by [`prisma-markdown`](https://github.com/samchon/prisma-markdown)

- [default](#default)

## default
```mermaid
erDiagram
"tenants" {
  String id PK
  String name
  TenantType type
  String parent_tenant_id FK "nullable"
  DateTime created_at
  DateTime updated_at
}
"products" {
  String id PK
  String tenant_id FK
  String name
  String description
  DateTime created_at
  DateTime updated_at
}
"endpoints" {
  String id PK
  String tenant_id FK
  String product_id FK
  String name UK
  String url
  String description "nullable"
  Json additional_data
  DateTime created_at
  DateTime updated_at
}
"test_triggers" {
  String id PK
  String tenant_id FK
  String endpoint_id FK
  String schedule
  DateTime created_at
  DateTime updated_at
}
"test_suites" {
  String id PK
  String tenant_id FK "nullable"
  String name UK
  String description
  DateTime created_at
  DateTime updated_at
}
"tests" {
  String id PK
  String name UK
  String title
  String description
  String applicability
  String outcome
  String tags
  Json additional_data
  DateTime created_at
  DateTime updated_at
}
"test_suite_mappings" {
  String test_suite_id FK
  String test_id FK
  DateTime created_at
  DateTime updated_at
}
"test_datasets" {
  String id PK
  String name UK
  String description
  Json data
  DateTime created_at
  DateTime updated_at
}
"test_dataset_mappings" {
  String test_id FK
  String test_dataset_id FK
  DateTime created_at
  DateTime updated_at
}
"test_runs" {
  String id PK
  String tenant_id FK
  String endpoint_id FK
  String name UK
  TestTriggerer triggered_by "nullable"
  TestRunType type
  TestStatus status
  DateTime start_time
  DateTime end_time "nullable"
  String result "nullable"
  Int total_tests "nullable"
  Int total_passed "nullable"
  Int total_prompts "nullable"
  Int total_prompts_passed "nullable"
  Json additional_data
  DateTime created_at
  DateTime updated_at
}
"test_run_test_results" {
  String id PK
  String tenant_id FK
  String test_run_id FK
  String test_id FK
  TestStatus status
  String result
  DateTime start_time "nullable"
  DateTime end_time "nullable"
  Int total_prompts
  Int total_prompts_passed
  DateTime created_at
  DateTime updated_at
}
"test_run_prompt_results" {
  String id PK
  String tenant_id FK
  String test_run_test_result_id FK
  String test_id FK "nullable"
  String test_dataset_id FK "nullable"
  String prompt_id
  TestStatus status
  String input
  String output
  String expected
  String evaluator
  String result
  DateTime start_time "nullable"
  DateTime end_time "nullable"
  Float duration "nullable"
  DateTime created_at
  DateTime updated_at
}
"tenants" }o--o| "tenants" : parent_tenant
"products" }o--|| "tenants" : tenant
"endpoints" }o--|| "tenants" : tenant
"endpoints" }o--|| "products" : product
"test_triggers" }o--|| "tenants" : tenant
"test_triggers" }o--|| "endpoints" : endpoint
"test_suites" }o--o| "tenants" : tenant
"test_suite_mappings" }o--|| "test_suites" : test_suite
"test_suite_mappings" }o--|| "tests" : test
"test_dataset_mappings" }o--|| "tests" : test
"test_dataset_mappings" }o--|| "test_datasets" : test_dataset
"test_runs" }o--|| "tenants" : tenant
"test_runs" }o--|| "endpoints" : endpoint
"test_run_test_results" }o--|| "tenants" : tenant
"test_run_test_results" }o--|| "test_runs" : test_run
"test_run_test_results" }o--|| "tests" : test
"test_run_prompt_results" }o--|| "tenants" : tenant
"test_run_prompt_results" }o--|| "test_run_test_results" : test_run_test_result
"test_run_prompt_results" }o--o| "test_datasets" : test_dataset
"test_run_prompt_results" }o--o| "tests" : test
```

### `tenants`

**Properties**
  - `id`: 
  - `name`: 
  - `type`: 
  - `parent_tenant_id`: 
  - `created_at`: 
  - `updated_at`: 

### `products`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `name`: 
  - `description`: 
  - `created_at`: 
  - `updated_at`: 

### `endpoints`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `product_id`: 
  - `name`: 
  - `url`: 
  - `description`: 
  - `additional_data`: 
  - `created_at`: 
  - `updated_at`: 

### `test_triggers`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `endpoint_id`: 
  - `schedule`: 
  - `created_at`: 
  - `updated_at`: 

### `test_suites`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `name`: 
  - `description`: 
  - `created_at`: 
  - `updated_at`: 

### `tests`

**Properties**
  - `id`: 
  - `name`: 
  - `title`: 
  - `description`: 
  - `applicability`: 
  - `outcome`: 
  - `tags`: 
  - `additional_data`: 
  - `created_at`: 
  - `updated_at`: 

### `test_suite_mappings`

**Properties**
  - `test_suite_id`: 
  - `test_id`: 
  - `created_at`: 
  - `updated_at`: 

### `test_datasets`

**Properties**
  - `id`: 
  - `name`: 
  - `description`: 
  - `data`: 
  - `created_at`: 
  - `updated_at`: 

### `test_dataset_mappings`

**Properties**
  - `test_id`: 
  - `test_dataset_id`: 
  - `created_at`: 
  - `updated_at`: 

### `test_runs`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `endpoint_id`: 
  - `name`: 
  - `triggered_by`: 
  - `type`: 
  - `status`: 
  - `start_time`: 
  - `end_time`: 
  - `result`: 
  - `total_tests`: 
  - `total_passed`: 
  - `total_prompts`: 
  - `total_prompts_passed`: 
  - `additional_data`: 
  - `created_at`: 
  - `updated_at`: 

### `test_run_test_results`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `test_run_id`: 
  - `test_id`: 
  - `status`: 
  - `result`: 
  - `start_time`: 
  - `end_time`: 
  - `total_prompts`: 
  - `total_prompts_passed`: 
  - `created_at`: 
  - `updated_at`: 

### `test_run_prompt_results`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `test_run_test_result_id`: 
  - `test_id`: 
  - `test_dataset_id`: 
  - `prompt_id`: 
  - `status`: 
  - `input`: 
  - `output`: 
  - `expected`: 
  - `evaluator`: 
  - `result`: 
  - `start_time`: 
  - `end_time`: 
  - `duration`: 
  - `created_at`: 
  - `updated_at`: 