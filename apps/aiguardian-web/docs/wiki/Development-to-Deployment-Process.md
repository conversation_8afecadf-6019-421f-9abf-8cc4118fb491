## DEV & SIT

```mermaid
sequenceDiagram
    actor <PERSON><PERSON><PERSON><PERSON> as PO/BA
    actor Dev
    actor <PERSON><PERSON>eer
    actor <PERSON><PERSON>ps
    participant SlackChannel
    participant <PERSON><PERSON><PERSON> as Local Dev
    participant DevEnv as Dev (Cloud)
    participant SIT

    box Development & Testing Environments
        participant LocalDev
        participant DevEnv
        participant SIT
    end

    PO_BA->>Dev: New stories/bugs
    Dev->>PO_BA: Share Implementation Plan
    Dev->>LocalDev: Start Development (parallel)

    LocalDev-->>Dev: Development Completed

    Dev->>DevPeer: Request Peer Testing
    Dev->>DevEnv: Deploy to Dev for demo (Optional)
    DevPeer->>PO_BA: Demo/Sharing session
    DevPeer->>PO_BA: Demo/Sharing session
    DevPeer-->>Dev: Peer Testing Feedback

    Dev->>DevPeer: Request Merge Approval
    DevPeer-->>Dev: Merge Approved
    Dev->>Dev: Merge to main

    SIT->>SIT: Auto Deployment after merge to main
    SIT->>SIT: Automatic End-to-End Regression Tests
    SIT-->>SlackChannel: Notify Regression Tests Completed

    alt Regression Tests Failed
        SlackChannel-->>Dev: Notify failure
        Dev->>LocalDev: Fix Bug
        LocalDev-->>Dev: Bug Fixed
        Dev->>DevPeer: Request Peer Review
        DevPeer-->>Dev: Peer Review Completed
        Dev->>Dev: Merge to main again
        SIT->>SIT: Auto Deployment after merge to main
        SIT->>SIT: Automatic End-to-End Regression Tests
        SIT-->>SlackChannel: Notify Regression Tests Completed
    else Regression Tests Succeeded
        DevPeer->>SIT: Additional Peer Testing (optional)
        PO_BA->>SIT: Additional User Testing (optional)
    end

    Dev->>DevEnv: Deploy to Dev Env
    DevEnv-->>Dev: Validate Deployment
```

## STG & PRD
```mermaid
sequenceDiagram
    actor PO_BA as PO/BA
    actor DevDevOps as Dev/DevOps
    actor TechLead
    participant STG
    participant PRD

    PO_BA->>DevDevOps: End of Sprint (Wed evening) / Hotfix
    DevDevOps->>TechLead: Request Deployment Approval for STG <br/>via Slack
    TechLead-->>DevDevOps: STG Deployment Approved

    DevDevOps->>STG: Deploy to STG
    STG-->>PO_BA: Notify PO/BA

    PO_BA->>STG: Post-Deployment User Acceptance Testing
    PO_BA->>DevDevOps: Completion of User Acceptance Testing <br/>& PRD Deployment Approval

    %% DevDevOps->>PO_BA: Request Deployment Approval for PRD <br/>via Slack
    %% PO_BA-->>DevDevOps: PRD Deployment Approved

    DevDevOps->>PRD: Deploy to Production (Thurs evening)
    PRD-->>PO_BA: Notify Production Deployment Completed
    DevDevOps->>PRD: Increased Production Monitoring

    PO_BA->>PRD: Post-Deployment Testing
```

Target Sprint Timeline:

![image](uploads/c092971f4b100915a13b5f175ae0fda4/image.png)