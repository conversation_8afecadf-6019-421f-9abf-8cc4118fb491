### AWS Bedrocks

Need to request for model access in both ap-southeast-1 and all US regions (for Llama 3.3, Claude 3.7)

### RDS

1. Go to AWS Secrets Manager and create 3 new Secrets for the env's RDS:

- `/dsaid/aig/<env>/litmus-web/db/uam`
- `/dsaid/aig/<env>/litmus-web/db/litmus`
- `/dsaid/aig/<env>/litmus-web/db/sentinel`

2. As root user, connect to `postgres` database:

    ```sql
    CREATE USER aiguardian_uam WITH CREATEDB PASSWORD 'password_created_above';
    CREATE USER aiguardian_litmus WITH CREATEDB PASSWORD 'password_created_above';
    CREATE USER aiguardian_sentinel WITH CREATEDB PASSWORD 'password_created_above';
    ```

3. As `aiguardian_uam` user:
 
    i. connect to `postgres` database:

   ```sql
   CREATE DATABASE aiguardian_uam;
   ```

    ii. connect to `aiguardian_uam` database and run all the init SQLs

4. Repeat the above for `aiguardian_litmus` and `aiguardian_sentinel`

5. As root user, connect to `postgres` database:
   ```
   ALTER USER aiguardian_uam NOCREATEDB;
   ALTER USER aiguardian_litmus NOCREATEDB;
   ALTER USER aiguardian_sentinel NOCREATEDB;
   ```