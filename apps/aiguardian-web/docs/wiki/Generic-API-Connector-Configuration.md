This document provides examples of how to use the new `auth_config` parameter in the Generic API Connector.

## Authentication Types

The following authentication types are supported:

1. **Header-based authentication** (`type: "header"`)
2. **No authentication** (`type: "none"`)

## Examples

### 1. Bearer Token Authentication

```js
{
  "product_name": "Vanilla Models Demo - Baseline Tests",
  "name": "sentosa",
  "connector_type": "generic-api-connector",
  "uri": "https://api.example.com/v1/completions",
  "token": "your-api-token",
  "max_calls_per_second": 10,
  "max_concurrency": 5,
  "model": "generic-model",
  "params": {
    "underlying_connector_type": "generic-api-connector",
    "input": "query.text",
    "output": "response.text",
    "auth_config": {
      "type": "header",
      "value": {
        "Authorization": "Bearer {token}"
      }
    }
  }
}
```

### 2. API Key in Header

```js
{
  "product_name": "Vanilla Models Demo - Baseline Tests",
  "name": "sentosa",
  "connector_type": "generic-api-connector",
  "uri": "https://api.example.com/v1/completions",
  "token": "your-api-key",
  "max_calls_per_second": 10,
  "max_concurrency": 5,
  "model": "generic-model",
  "params": {
    "underlying_connector_type": "generic-api-connector",
    "input": "query.text",
    "output": "response.text",
    "auth_config": {
      "type": "header",
      "value": {
        "x-api-key": "{token}"
      }
    }
  }
}
```

### 3. Multiple Headers

```js
{
  "product_name": "Vanilla Models Demo - Baseline Tests",
  "name": "sentosa",
  "connector_type": "generic-api-connector",
  "uri": "https://api.example.com/v1/completions",
  "token": "your-api-token",
  "max_calls_per_second": 10,
  "max_concurrency": 5,
  "model": "generic-model",
  "params": {
    "underlying_connector_type": "generic-api-connector",
    "input": "query.text",
    "output": "response.text",
    "auth_config": {
      "type": "header",
      "value": {
        "Authorization": "Bearer {token}",
        "x-custom-header": "custom-value"
      }
    }
  }
}
```

### 4. No Authentication

```js
{
  "product_name": "Vanilla Models Demo - Baseline Tests",
  "name": "sentosa",
  "connector_type": "generic-api-connector",
  "uri": "https://api.example.com/v1/completions",
  "token": "not-used",
  "max_calls_per_second": 10,
  "max_concurrency": 5,
  "model": "generic-model",
  "params": {
    "underlying_connector_type": "generic-api-connector",
    "input": "query.text",
    "output": "response.text",
    "auth_config": {
      "type": "none"
    }
  }
}
```

## Backward Compatibility

If no `auth_config` is provided, the connector will default to using `{"x-api-key": token}` for backward compatibility with existing endpoints.

```js
{
  "product_name": "Vanilla Models Demo - Baseline Tests",
  "name": "sentosa",
  "connector_type": "generic-api-connector",
  "uri": "https://api.example.com/v1/completions",
  "token": "your-api-token",
  "max_calls_per_second": 10,
  "max_concurrency": 5,
  "model": "generic-model",
  "params": {
    "underlying_connector_type": "generic-api-connector",
    "input": "query.text",
    "output": "response.text"
    // No auth_config provided, defaults to {"x-api-key": token}
  }
}
```
