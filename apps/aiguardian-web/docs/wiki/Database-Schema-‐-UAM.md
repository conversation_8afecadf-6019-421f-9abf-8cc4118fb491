# AIG UAM database models
> Generated by [`prisma-markdown`](https://github.com/samchon/prisma-markdown)

- [default](#default)

## default
```mermaid
erDiagram
"tenants" {
  String id PK
  String name
  TenantType type
  String parent_tenant_id FK "nullable"
  DateTime created_at
  DateTime updated_at
}
"products" {
  String id PK
  String tenant_id FK
  String name
  String description "nullable"
  DateTime created_at
  DateTime updated_at
}
"endpoints" {
  String id PK
  String product_id FK
  String name UK
  String url
  String description "nullable"
  Json additional_data
  DateTime created_at
  DateTime updated_at
}
"users" {
  String id PK
  String external_id "nullable"
  String email "nullable"
  String pwd_salt "nullable"
  Int pwd_hash_iterations "nullable"
  String pwd_hash "nullable"
  String name
  DateTime created_at
  DateTime updated_at
}
"roles" {
  String id PK
  String full_name UK
  String short_name UK
  String description "nullable"
}
"user_tenant_roles" {
  String id PK
  String user_id FK
  String tenant_id FK
  String role_id FK
  Boolean is_active
  DateTime created_at
  DateTime updated_at
}
"aig_api_keys" {
  String id PK
  String tenant_id FK
  String service
  String api_key_id
  DateTime created_at
  DateTime updated_at
}
"permissions" {
  String id PK
  String short_name UK
  String full_name UK
  String description
  DateTime created_at
  DateTime updated_at
}
"role_permissions" {
  String id PK
  String role_id FK
  String permission_id FK
  DateTime created_at
  DateTime updated_at
}
"tenants" }o--o| "tenants" : parent_tenant
"products" }o--|| "tenants" : tenant
"endpoints" }o--|| "products" : product
"user_tenant_roles" }o--|| "users" : user
"user_tenant_roles" }o--|| "tenants" : tenant
"user_tenant_roles" }o--|| "roles" : role
"aig_api_keys" }o--|| "tenants" : tenant
"role_permissions" }o--|| "roles" : role
"role_permissions" }o--|| "permissions" : permission
```

### `tenants`

**Properties**
  - `id`: 
  - `name`: 
  - `type`: 
  - `parent_tenant_id`: 
  - `created_at`: 
  - `updated_at`: 

### `products`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `name`: 
  - `description`: 
  - `created_at`: 
  - `updated_at`: 

### `endpoints`

**Properties**
  - `id`: 
  - `product_id`: 
  - `name`: 
  - `url`: 
  - `description`: 
  - `additional_data`: 
  - `created_at`: 
  - `updated_at`: 

### `users`

**Properties**
  - `id`: 
  - `external_id`: 
  - `email`: 
  - `pwd_salt`: 
  - `pwd_hash_iterations`: 
  - `pwd_hash`: 
  - `name`: 
  - `created_at`: 
  - `updated_at`: 

### `roles`

**Properties**
  - `id`: 
  - `full_name`: 
  - `short_name`: 
  - `description`: 

### `user_tenant_roles`

**Properties**
  - `id`: 
  - `user_id`: 
  - `tenant_id`: 
  - `role_id`: 
  - `is_active`: 
  - `created_at`: 
  - `updated_at`: 

### `aig_api_keys`

**Properties**
  - `id`: 
  - `tenant_id`: 
  - `service`: 
  - `api_key_id`: 
  - `created_at`: 
  - `updated_at`: 

### `permissions`

**Properties**
  - `id`: 
  - `short_name`: 
  - `full_name`: 
  - `description`: 
  - `created_at`: 
  - `updated_at`: 

### `role_permissions`

**Properties**
  - `id`: 
  - `role_id`: 
  - `permission_id`: 
  - `created_at`: 
  - `updated_at`: 