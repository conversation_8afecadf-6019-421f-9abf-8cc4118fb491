This guide will provide instructions to application setup and enabling features to support the OAuth 2.1's Auth Code Flow with Proof Key for Code Exchange (PKCE)

## Authentication

### Tenant Admin Required Steps to use Techpass Auth Code Flow

We need a Tenant Admin of a TechPass Tenant to flow this instruction to create/gather significant information.
[Prerequisites](https://docs.developer.tech.gov.sg/docs/techpass-tenant-guide/concepts/authcodegrant?id=prerequisites)

- [ ] Create an application including these information below
    - Application name
    - Redirect URLs.
- [ ] Create secret.
- [ ] Gather the endpoints and configurations required for your portal

### Implement Auth Code Flow
1. Generates the login URL (Frontend) and get the code in SUCCESSFUL RESPONSE
2. Request for token (Backend) with returned code to Microsoft. A successful token response will look like:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6Ik5HVEZ2ZEstZnl0aEV1Q...",
  "token_type": "Bearer",
  "expires_in": 3599,
  "scope": "https%3A%2F%2Fgraph.microsoft.com%2Fmail.read",
  "refresh_token": "AwABAAAAvPM1KaPlrEqdFSBzjqfTGAMxZGUTdM0t4B4...",
  "id_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.eyJhdWQiOiIyZDRkMTFhMi1mODE0LTQ2YTctOD...",
}
```
3. Verify ID Token's Signature.
- Example of JWT claims
```json
{
  "aud": "35e70778-ab39-4410-87df-b36b9463e2bd",
  "iss": "https://login.microsoftonline.com/1a86bb32-c02c-481b-8b67-05f7382f6446/v2.0",
  "iat": **********,
  "nbf": **********,
  "exp": **********,
  "email": "<EMAIL>",
  "family_name": "Skywalker",
  "given_name": "Luke",
  "name": "Luke Skywalker",
  "oid": "da4b44c3-ab99-464a-9999-abcd99999999",
  "preferred_username": "<EMAIL>",
  "sub": "abcdefghijklmnopqrstuvwxyz",
  "upn": "luke_skywalker_tech.gov.sg#EXT#@gdstechpassstg.onmicrosoft.com",
  "tp_acct_typ": "account:public_officer"
}
```


- Read more: [Verify ID Token's Signature](https://docs.developer.tech.gov.sg/docs/techpass-tenant-guide/concepts/authcodegrant?id=_6-verify-id-tokens-signature)
4. Refresh Tokens
- Read more: [Refresh the access token](https://learn.microsoft.com/en-us/entra/identity-platform/v2-oauth2-auth-code-flow#refresh-the-access-token)


## Authorization

### AIGuardian Approach
- Mapping of users to tenants to roles: managed in TechPass with Groups & Roles
- Mapping of roles to permissions: managed in Auth Service using JSON mapping
```
{
  "userId": "<EMAIL>",
  "permissions": {
    "tenant_id_1": [
      "litmus_view_dashboard_analytics",
      "...",
    ],
    "tenant_id_2": [
      "run_ad_hoc_test",
      "...",
    ]
  }
}
```
- Tenant Management UI: new app in monorepo


### Token Conversion Process Steps

**1. Token Receipt**

- Auth Backend receives TechPass tokens.
- Access token will be decouple to user roles and groups.

**2. Role Extraction**

- Extract roles from access token
- Look up corresponding permissions
- Aggregate permissions for all roles and remove duplicates
- Validate permission set

**3. Issue a new JWT token**

- Define token payload structure:
```json
{
  "sub": "<user_id>", # internal User ID
  "info": {
    "email": "<EMAIL>",
    "...", # e.g: name, tenant name, ....
    "permissions": {
      "tenant_id_1": [
        "litmus_view_dashboard_analytics",
        "...",
      ],
      "tenant_id_2": [
        "run_ad_hoc_test",
        "...",
      ]
    }
  }
  "iat": "<timestamp>",
  "exp": "<expiration>",
  "iss": "aig-auth-backend"
}
```

**Performance**
- Optimize token size
- Cache frequently used mappings
- Monitor token processing time

## Auth Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant AuthFrontend as AIG Auth Frontend
    participant AuthBackend as AIG Auth Backend
    participant LitmusWebFrontend as Litmus Web Frontend
    participant LitmusWebBackend as Litmus Web Backend
    participant AADTechPass as AAD via TechPass

    User ->> AuthFrontend: Lands on web portal
    AuthFrontend ->> User: Displays homepage
    User ->> AuthFrontend: Clicks on "Sign in via TechPass" button
    AuthFrontend ->> AuthFrontend: Generate code verifier and code challenge
    AuthFrontend ->> AADTechPass: Routes to TechPass sign-in page using /authorize url
    Note right of AuthFrontend: Specify: <br/>- Application ID<br/>- Scope<br/>- Auth code grant type<br/>- Redirect URL<br/>- Code challenge<br/>- Code challenge method (S256)<br/>- State (random string)
    AADTechPass ->> User: Displays TechPass sign-in page
    User ->> AADTechPass: Signs in with password + MFA
    AADTechPass ->> AADTechPass: Authenticates user

    alt Successful
        AADTechPass ->> AuthFrontend: Returns unique code, state
        AuthFrontend ->> AuthFrontend: Verify state values match
        AuthFrontend ->> AuthBackend: Forwards code, app ID, auth grant type, scope, code verifier, redirect URL
        AuthBackend ->> AADTechPass: Exchange for ID, access, and refresh tokens
        AADTechPass ->> AuthBackend: Sends UserID/Email, and refresh tokens
        AuthBackend ->> AuthBackend: Stores tokens securely, convert UserId/Email to roles and permissions, creates a session
        AuthBackend ->> AuthFrontend: Returns session info (userId, permissions)
        AuthFrontend ->> User: Displays successful sign-in
        Note right of AuthFrontend: Application manages user's session after successful sign-in
        User ->> LitmusWebFrontend: Use JWT to access LitmusWeb
        LitmusWebFrontend ->> LitmusWebBackend: Forward JWT
        LitmusWebBackend ->> LitmusWebBackend: Determine user & get permissions
    else Mismatch
        AuthFrontend ->> User: Rejects sign-in
    end

    alt Failure
        AADTechPass ->> User: Display failure to sign in error
    end
```

Original Diagram from Techpass
![Image](https://github.com/user-attachments/assets/7bf0878b-2ca1-4209-b34a-ee58ebdf7f1b)


## Checking Permission

The AIGuardian platform implements a robust permission checking mechanism via the AuthGuard, which performs both authentication and authorization.

### Authentication Methods

The system supports two authentication methods:

1. **JWT Token Authentication** (primary method)
   - Requires `Authorization: Bearer <token>` header
   - Token contains user payload with permissions mapped by tenant

2. **API Key Authentication** (secondary method)
   - Requires `x-api-key` header
   - Used primarily for service-to-service communication

### Permission Checking Process

For JWT Authentication, the permission checking follows this flow:

1. **Extract JWT Token**
   - Token is extracted from the Authorization header
   - Token signature is verified using the JWT secret

2. **Tenant Identification**
   - Tenant ID is required via the `x-tenant-id` header
   - This header is mandatory for tenant-specific operations

3. **Permission Verification**
   - The required permission is extracted using the `@RequirePermission` decorator
   - User permissions are extracted from the JWT payload for:
     - The specific tenant ID
     - Global permissions (tenant ID = '*')
   - The system checks if the user has the required permission

4. **Permission Format**
   - Permissions follow a consistent naming pattern: `{product}_{resource}_{action}` (e.g. `ltm_tests_create`)
   - Shorthand format: `{product}_{r}_{a}` (e.g., `ltm_t_r` = Litmus Test Read)
   - Common actions: `c` (create), `r` (read), `u` (update), `d` (delete)

### Example Implementation

Controllers use the `@RequirePermission` decorator to specify required permissions:

```typescript
@Get('tests')
@RequirePermission({ permission: Permissions.ltm_tests_create })
@UseGuards(AuthGuard)
async getTests() {
  // This endpoint requires the "ltm_t_r" (Litmus Tests Read) permission
  // The AuthGuard will verify the user has this permission
  // ...
}
```

To use the authenticated user in a controller:

```typescript
@Get('user-info')
@RequirePermission({ permission: Permissions.um_users_read })
@UseGuards(AuthGuard)
async getUserInfo(@AuthedUser() user: AuthedUserPayload) {
  // Access user information from the decoded JWT
  const { id, name, tenantId, permissions } = user;
  // ...
}
```

### Error Handling

The permission checking mechanism throws standardized exceptions:
- `UnauthorizedException`: Missing token, invalid token, or missing tenant ID
- `ForbiddenException`: Valid token but insufficient permissions

## Refresh Token Flow Diagram

```mermaid

sequenceDiagram
    participant F as Next.js Frontend
    participant B as UAM
    participant D as DB
    participant A as AAD Techpass

    Note over F,A: Token Refresh Flow (Access Token Expired)
    F->>B: 1. API request with expired Access Token
    B->>F: 2. Return 401 Unauthorized
    F->>B: 3. Call refresh endpoint with Refresh Token
    B->>D: 4. Validate refresh token in DB
    D->>B: 5. Return validation result
    alt Refresh token valid in DB
        B->>A: 6. Validate user still exists/has access in Azure AD
        A->>B: 7. Return user validation result
        alt User valid in Azure AD
            B->>B: 8. Generate new Access Token (5min)
            B->>F: 9. Return new Access Token (RT unchanged)
            F->>F: 10. Update stored Access Token
            F->>B: 11. Retry original request with new Access Token
            B->>F: 12. Return API response
        else User removed/no access in Azure AD
            B->>D: 13. Delete refresh token from DB
            B->>F: 14. Return 401 Unauthorized
            F->>F: 15. Redirect to login
        end
    else Refresh token invalid/expired in DB
        B->>F: 16. Return 401 Unauthorized
        F->>F: 17. Redirect to login
    end
```

## References
- [Design models for OPA](https://docs.aws.amazon.com/prescriptive-guidance/latest/saas-multitenant-api-access-authorization/using-opa.html)
- [Auth Code Flow with Proof Key for Code Exchange (PKCE)](https://docs.developer.tech.gov.sg/docs/techpass-tenant-guide/concepts/authcodegrant?id=auth-code-flow-with-proof-key-for-code-exchange-pkce)
- [Role Based Access Control Using App Roles](https://docs.developer.tech.gov.sg/docs/techpass-tenant-guide/concepts/rbac?id=role-based-access-control-using-app-roles)
- [Guidance for Multi-Tenant Architectures on AWS](https://aws.amazon.com/solutions/guidance/multi-tenant-architectures-on-aws/)
