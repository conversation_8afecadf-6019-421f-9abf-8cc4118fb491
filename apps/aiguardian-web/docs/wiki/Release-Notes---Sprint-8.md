# AI Guardian Release Notes - Sprint 8 (March 2025)

## 🚀 Highlights for Business Stakeholders
- **Enhanced User Onboarding**: Streamlined onboarding for government agencies (MTI, MOF, AGC) to accelerate adoption.
- **Improved Security**: Comprehensive JWT protection and advanced security scanning to safeguard sensitive data.
- **Operational Efficiency**: Migration to internal ELB and GPU time-sharing in EKS for cost-effective resource utilization.

## 🛡️ Sentinel Advancements
- **AWS PII Detection**: Integration with AWS PII guardrails to enhance data privacy compliance.
- **OpenAI Moderation**: Added moderation services to ensure safe and ethical AI usage.
- **Bedrock Embeddings**: Migration to Amazon Bedrock embeddings for improved system-prompt-leakage protection.
- **Database Architecture**: New database design for reliable guardrail validation tracking.

## 🧪 Litmus Testing Framework
- **Cross-Platform CI/CD**: Integration with both GitHub and GitLab for seamless testing workflows.
- **Enhanced Test Management**: New test library and dashboard visualizations for better insights.
- **End-to-End Testing**: Expanded test coverage for endpoints, test runs, and dashboards.

## 🛠️ Technical Improvements
- **Litmus-API Migration**: Transitioned APIs to a new NestJS-based structure for better scalability and maintainability.
- **Common Component Library**: Consolidated shared components into `@aiguardian/ui` and `@aiguardian/utils` for reusability.
- **Security Enhancements**: Enabled GitLab SAST and DAST scanning for proactive vulnerability detection.

## ⚙️ DevOps Enhancements
- **Streamlined Access**: AWS CLI access via `aws-vault` and improved database access for developers.
- **New CI/SIT Environment**: Dedicated environment for reliable end-to-end testing.
- **Infrastructure Optimization**: Migrated web traffic to internal ELB for enhanced security and performance.

## 📖 Learn More
For detailed documentation and support, visit our [AI Guardian Knowledge Base](#).

We thank our stakeholders and users for their continued support as we work to make AI safer, more reliable, and accessible for government applications.