# AIGuardian Wiki Content Analysis
*Generated: Day 1-2 of Knowledge Base Ingestion Process Design*

## 📊 **Content Inventory Summary**

### **File Statistics**
- **Total Files**: 41 markdown files
- **Total Lines**: 3,393 lines
- **Average File Size**: ~83 lines per file
- **Size Range**: 0-306 lines per file

### **Content Distribution by Size**
| Size Category | Line Range | Count | Files |
|---------------|------------|-------|-------|
| **Large** | 200+ lines | 5 | Database schemas, API guides, Auth docs |
| **Medium** | 50-199 lines | 20 | Product overviews, onboarding guides |
| **Small** | 1-49 lines | 15 | Quick references, checklists |
| **Empty** | 0 lines | 1 | home.md |

## 🗂️ **Content Categorization**

### **1. Product Overview & Core Concepts** (8 files)
**Purpose**: High-level product information and value propositions
- `AIGuardian-Services-‐-Litmus-&-Sentinel.md` (122 lines) - System overview
- `Litmus-Overview.md` (53 lines) - Product overview
- `Sentinel-Overview.md` (54 lines) - Product overview  
- `Architecture.md` (29 lines) - System architecture
- `Moonshot.md` (142 lines) - Core testing engine
- `Sentinel-Guardrails.md` (53 lines) - Guardrail concepts
- `Sentinel-Demo.md` (7 lines) - Demo information
- `Who's-Who.md` (87 lines) - Team and contacts

**Chatbot Relevance**: ⭐⭐⭐⭐⭐ (Critical for "What is..." queries)

### **2. Onboarding & Getting Started** (6 files)
**Purpose**: Step-by-step guidance for new users
- `Litmus-Onboarding-Guide.md` (110 lines) - Complete onboarding process
- `Sentinel-Onboarding-Guide.md` (230 lines) - Complete onboarding process
- `Litmus-Getting-Started.md` (75 lines) - Quick start guide
- `New-Tenant-Onboarding.md` (91 lines) - Tenant setup process
- `Onboarding-Checklist.md` (13 lines) - Quick checklist
- `Onboarding-for-new-engineers.md` (33 lines) - Developer onboarding

**Chatbot Relevance**: ⭐⭐⭐⭐⭐ (Critical for "How to..." queries)

### **3. API Documentation & Technical Integration** (7 files)
**Purpose**: Technical implementation guidance
- `Sentinel-APIs-‐-User-Guide.md` (292 lines) - Complete API documentation
- `Sentinel-APIs.md` (133 lines) - API reference
- `Generic-API-Connector-Configuration.md` (135 lines) - Integration patterns
- `Litmus-Moonshot-Integration.md` (79 lines) - Technical integration
- `Moonshot‐Sentinel-integration.md` (24 lines) - Integration details
- `Test-Information-Documentation.md` (46 lines) - Test specifications
- `GPU-vs-CPU-performance-testing.md` (51 lines) - Performance guidance

**Chatbot Relevance**: ⭐⭐⭐⭐⭐ (Critical for technical queries)

### **4. Database & Schema Documentation** (5 files)
**Purpose**: Data structure and database information
- `Database-Schema-‐-Litmus.md` (306 lines) - Complete Litmus schema
- `Database-Schema-‐-UAM.md` (185 lines) - UAM schema
- `Database-Schema-‐-Sentinel.md` (105 lines) - Sentinel schema
- `Database-Schema.md` (66 lines) - General schema info
- `Database-Access.md` (14 lines) - Access patterns

**Chatbot Relevance**: ⭐⭐⭐ (Moderate - for advanced technical queries)

### **5. Authentication & Security** (2 files)
**Purpose**: Security and access control information
- `Authentication-&-Authorization-using-Techpass.md` (285 lines) - Auth system
- `Permission-Matrix.md` (142 lines) - Access control matrix

**Chatbot Relevance**: ⭐⭐⭐⭐ (High for security-related queries)

### **6. Environment & Deployment** (5 files)
**Purpose**: Environment setup and deployment guidance
- `Development-to-Deployment-Process.md` (89 lines) - Deployment process
- `Environments.md` (13 lines) - Environment overview
- `New-Environment-Setup.md` (37 lines) - Setup instructions
- `Routing-for-Litmus-&-Sentinel-across-Environments.md` (10 lines) - Routing
- `Using-AWS-with-Localstack.md` (9 lines) - Local development

**Chatbot Relevance**: ⭐⭐⭐ (Moderate for deployment queries)

### **7. Developer Resources** (4 files)
**Purpose**: Development tools and processes
- `Onboarding-‐-Python.md` (71 lines) - Python development
- `Onboarding-‐-Typescript.md` (45 lines) - TypeScript development
- `Submodules.md` (36 lines) - Git submodule management
- `Release-Notes---Sprint-8.md` (31 lines) - Release information

**Chatbot Relevance**: ⭐⭐ (Low - internal development focus)

### **8. Troubleshooting & Support** (2 files)
**Purpose**: Problem resolution and support
- `Litmus-Troubleshooting.md` (17 lines) - Common issues
- `Offboarding-Checklist.md` (13 lines) - Offboarding process

**Chatbot Relevance**: ⭐⭐⭐⭐ (High for support queries)

### **9. Navigation & Meta** (2 files)
**Purpose**: Wiki navigation and structure
- `_Sidebar.md` (60 lines) - Navigation structure
- `home.md` (0 lines) - Empty homepage

**Chatbot Relevance**: ⭐ (Low - structural only)

## 🎯 **Content Quality Analysis**

### **High-Quality Content** (Ready for Chatbot)
✅ **Well-Structured**: Clear headers, consistent formatting
✅ **Complete Information**: Comprehensive coverage of topics
✅ **User-Focused**: Written for end-users, not just developers

**Examples**:
- `Litmus-Overview.md` - Clear product explanation with benefits
- `Sentinel-APIs-‐-User-Guide.md` - Complete API documentation with examples
- `Litmus-Onboarding-Guide.md` - Step-by-step user guidance

### **Medium-Quality Content** (Needs Processing)
⚠️ **Technical Heavy**: Requires context for non-technical users
⚠️ **Incomplete**: Some sections need expansion

**Examples**:
- Database schema files - Very technical, need simplification
- Integration guides - Assume technical knowledge

### **Low-Quality Content** (Minimal Value)
❌ **Too Brief**: Insufficient information for meaningful responses
❌ **Internal Focus**: Not relevant for external users

**Examples**:
- `home.md` - Empty file
- Some checklist files - Too brief for context

## 🔗 **Content Relationships & Cross-References**

### **Primary Content Clusters**
1. **Litmus Ecosystem**: Overview → Getting Started → Onboarding → API Integration
2. **Sentinel Ecosystem**: Overview → Onboarding → API Guide → Guardrails
3. **Authentication Flow**: Techpass → UAM → Permissions → Access
4. **Technical Integration**: API Docs → Configuration → Troubleshooting

### **Cross-Reference Patterns**
- Product overviews link to onboarding guides
- API documentation references authentication
- Onboarding guides reference troubleshooting
- Architecture documents reference all components

## 📝 **Content Metadata Schema Design**

Based on analysis, recommended metadata structure:

```typescript
interface DocumentMetadata {
  // File Information
  source_file: string;           // "Litmus-Overview.md"
  file_size: number;            // Line count
  last_modified: Date;          // File modification date
  
  // Content Classification
  category: 'product_overview' | 'onboarding' | 'api_documentation' | 
           'database_schema' | 'authentication' | 'environment' | 
           'developer_resources' | 'troubleshooting' | 'navigation';
  
  product: 'litmus' | 'sentinel' | 'aiguardian' | 'moonshot' | 'uam' | 'general';
  
  audience: 'beginner' | 'intermediate' | 'advanced' | 'developer' | 'admin';
  
  content_type: 'overview' | 'guide' | 'reference' | 'tutorial' | 
                'troubleshooting' | 'schema' | 'checklist';
  
  // Relevance Scoring
  chatbot_priority: 1 | 2 | 3 | 4 | 5;  // 5 = highest priority
  
  // Content Features
  has_code_examples: boolean;
  has_images: boolean;
  has_mermaid_diagrams: boolean;
  has_external_links: boolean;
  
  // Relationships
  related_files: string[];       // Cross-referenced documents
  prerequisites: string[];       // Required reading
  follow_up: string[];          // Next steps
}
```

## 🎯 **Key Insights for Chatbot Design**

### **1. Query Pattern Mapping**
- **"What is..."** → Product Overview category (8 files)
- **"How to..."** → Onboarding & API Documentation (13 files)
- **"Integration..."** → API Documentation & Technical Integration (7 files)
- **"Troubleshooting..."** → Troubleshooting & Support (2 files)

### **2. Content Prioritization**
**Priority 1 (Critical)**: 21 files covering product overviews, onboarding, API docs
**Priority 2 (Important)**: 12 files covering auth, environment, troubleshooting  
**Priority 3 (Supporting)**: 8 files covering database, developer resources

### **3. Processing Strategy**
- **Semantic Chunking**: Chunk by headers and logical sections
- **Context Preservation**: Maintain file-level context for attribution
- **Cross-Reference Linking**: Build relationship graph between documents
- **Quality Filtering**: Prioritize high-quality, user-focused content

**Next Steps**: Proceed to Day 3-4 Content Processing Strategy Design
