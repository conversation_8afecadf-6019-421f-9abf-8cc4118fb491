# Content Processing Strategy Design
*Generated: Day 3-4 of Knowledge Base Ingestion Process Design*

## 🎯 **Processing Pipeline Architecture**

```mermaid
graph TD
    A[Wiki Files] --> B[Document Parser]
    B --> C[Metadata Extractor]
    C --> D[Content Preprocessor]
    D --> E[Semantic Chunker]
    E --> F[Cross-Reference Linker]
    F --> G[Embedding Generator]
    G --> H[Vector Database]
    
    I[File Watcher] --> B
    J[Quality Validator] --> D
    K[Version Controller] --> C
```

## 📄 **Document Parsing Pipeline**

### **Stage 1: Document Parser**
**Input**: Raw markdown files from `/apps/aiguardian-web/docs/wiki/`
**Output**: Structured document objects

```typescript
interface ParsedDocument {
  metadata: {
    filename: string;
    filepath: string;
    size: number;
    lastModified: Date;
    checksum: string;
  };
  content: {
    title: string;
    headers: Header[];
    sections: Section[];
    codeBlocks: CodeBlock[];
    images: Image[];
    links: Link[];
    mermaidDiagrams: MermaidDiagram[];
  };
  rawContent: string;
}

interface Section {
  id: string;
  level: number;           // H1=1, H2=2, etc.
  title: string;
  content: string;
  startLine: number;
  endLine: number;
  subsections: Section[];
}
```

**Processing Rules**:
1. **Header Extraction**: Parse H1-H6 headers for structure
2. **Section Identification**: Group content by header hierarchy
3. **Code Block Detection**: Extract and preserve code examples
4. **Image Processing**: Catalog images and alt text
5. **Link Extraction**: Identify internal and external links
6. **Mermaid Diagram Parsing**: Extract diagram definitions

### **Stage 2: Metadata Extractor**
**Input**: Parsed document objects
**Output**: Enhanced documents with metadata

```typescript
interface DocumentMetadata {
  // File Classification
  category: ContentCategory;
  product: ProductType;
  audience: AudienceLevel;
  contentType: ContentType;
  priority: 1 | 2 | 3 | 4 | 5;
  
  // Content Features
  hasCodeExamples: boolean;
  hasImages: boolean;
  hasMermaidDiagrams: boolean;
  hasExternalLinks: boolean;
  wordCount: number;
  estimatedReadingTime: number;
  
  // Quality Metrics
  completeness: number;     // 0-1 score
  clarity: number;          // 0-1 score
  userFriendliness: number; // 0-1 score
  
  // Relationships
  relatedFiles: string[];
  prerequisites: string[];
  followUp: string[];
  crossReferences: CrossReference[];
}

type ContentCategory = 
  | 'product_overview' 
  | 'onboarding' 
  | 'api_documentation' 
  | 'database_schema' 
  | 'authentication' 
  | 'environment' 
  | 'developer_resources' 
  | 'troubleshooting' 
  | 'navigation';

type ProductType = 'litmus' | 'sentinel' | 'aiguardian' | 'moonshot' | 'uam' | 'general';
type AudienceLevel = 'beginner' | 'intermediate' | 'advanced' | 'developer' | 'admin';
type ContentType = 'overview' | 'guide' | 'reference' | 'tutorial' | 'troubleshooting' | 'schema' | 'checklist';
```

**Extraction Rules**:
1. **Category Detection**: Filename and content analysis
2. **Product Identification**: Keyword matching and context analysis
3. **Audience Assessment**: Technical complexity and language analysis
4. **Quality Scoring**: Content completeness and structure evaluation
5. **Relationship Discovery**: Link analysis and content similarity

### **Stage 3: Content Preprocessor**
**Input**: Documents with metadata
**Output**: Cleaned and normalized content

**Processing Steps**:
1. **Content Cleaning**:
   - Remove empty sections
   - Normalize whitespace
   - Fix broken links
   - Standardize formatting

2. **Content Enhancement**:
   - Add context for code examples
   - Expand abbreviations
   - Add product context where missing
   - Normalize terminology

3. **Quality Validation**:
   - Check for minimum content length
   - Validate link integrity
   - Ensure proper header hierarchy
   - Flag incomplete sections

## 🧩 **Semantic Chunking Strategy**

### **Chunking Approach: Hierarchical Semantic Chunking**

```typescript
interface ContentChunk {
  id: string;
  documentId: string;
  chunkType: 'section' | 'subsection' | 'code_example' | 'overview';
  
  // Content
  title: string;
  content: string;
  context: string;          // Surrounding context for understanding
  
  // Positioning
  startLine: number;
  endLine: number;
  hierarchyPath: string[];  // ['Overview', 'What is Litmus', 'Benefits']
  
  // Metadata
  wordCount: number;
  tokenCount: number;       // Estimated tokens for LLM
  priority: number;         // Relevance priority
  
  // Relationships
  parentChunk?: string;
  childChunks: string[];
  relatedChunks: string[];
  
  // Search Optimization
  keywords: string[];
  searchTerms: string[];
  embedding?: number[];
}
```

### **Chunking Rules**

#### **1. Primary Chunking (Section-Based)**
- **Chunk Boundary**: Major headers (H1, H2)
- **Target Size**: 200-800 tokens per chunk
- **Context Preservation**: Include parent header context

**Example**:
```
Document: "Litmus-Overview.md"
Chunk 1: "What is Litmus?" (H2 section)
Chunk 2: "Why use Litmus?" (H2 section)  
Chunk 3: "How does Litmus work?" (H2 section)
```

#### **2. Secondary Chunking (Subsection-Based)**
- **Trigger**: Primary chunks > 800 tokens
- **Chunk Boundary**: Minor headers (H3, H4)
- **Context**: Include parent section title

#### **3. Special Chunking (Content-Type Based)**
- **Code Examples**: Separate chunks with full context
- **API Endpoints**: Individual chunks per endpoint
- **Step-by-Step Guides**: One chunk per major step
- **Troubleshooting**: One chunk per issue/solution pair

### **Overlap Strategy**
- **Semantic Overlap**: 50-100 tokens between adjacent chunks
- **Context Overlap**: Include parent section title in child chunks
- **Cross-Reference Overlap**: Include related section summaries

## 🔗 **Cross-Reference Linking System**

### **Link Types**
```typescript
interface CrossReference {
  type: 'prerequisite' | 'follow_up' | 'related' | 'example' | 'definition';
  sourceChunk: string;
  targetChunk: string;
  strength: number;        // 0-1 relevance score
  description: string;
}
```

### **Link Discovery Methods**
1. **Explicit Links**: Markdown links between documents
2. **Keyword Matching**: Shared terminology and concepts
3. **Content Similarity**: Semantic similarity analysis
4. **User Journey Mapping**: Logical progression paths

### **Link Examples**
- `Litmus-Overview.md` → `Litmus-Getting-Started.md` (follow_up)
- `Sentinel-APIs.md` → `Authentication-&-Authorization.md` (prerequisite)
- `Litmus-Onboarding-Guide.md` → `Litmus-Troubleshooting.md` (related)

## 📊 **Version Control & Update Detection**

### **Change Detection Strategy**
```typescript
interface DocumentVersion {
  documentId: string;
  version: string;
  checksum: string;
  lastModified: Date;
  changes: Change[];
  processingStatus: 'pending' | 'processing' | 'completed' | 'failed';
}

interface Change {
  type: 'added' | 'modified' | 'deleted';
  section: string;
  startLine: number;
  endLine: number;
  impact: 'low' | 'medium' | 'high';
}
```

### **Update Processing Workflow**
1. **File Monitoring**: Watch for file system changes
2. **Change Analysis**: Identify modified sections
3. **Impact Assessment**: Determine reprocessing scope
4. **Incremental Update**: Update only affected chunks
5. **Relationship Refresh**: Update cross-references if needed

## 🎯 **Processing Optimization**

### **Performance Targets**
- **Initial Processing**: Complete wiki ingestion in < 5 minutes
- **Incremental Updates**: Process file changes in < 30 seconds
- **Memory Usage**: < 1GB for full wiki processing
- **Chunk Generation**: 95% of chunks within 200-800 token range

### **Quality Assurance**
- **Content Coverage**: 100% of high-priority content processed
- **Link Integrity**: 95% of internal links properly resolved
- **Chunk Quality**: 90% of chunks contain complete thoughts
- **Search Relevance**: 85% precision on test queries

## 🔧 **Implementation Architecture**

### **Core Components**
1. **DocumentProcessor**: Main orchestration service
2. **MarkdownParser**: Specialized markdown processing
3. **MetadataExtractor**: Content analysis and classification
4. **SemanticChunker**: Intelligent content chunking
5. **CrossReferenceBuilder**: Relationship discovery
6. **VersionManager**: Change tracking and updates
7. **QualityValidator**: Content quality assurance

### **Data Flow**
```
File System → DocumentProcessor → MarkdownParser → MetadataExtractor 
→ SemanticChunker → CrossReferenceBuilder → EmbeddingGenerator 
→ Vector Database
```

### **Configuration Management**
```typescript
interface ProcessingConfig {
  chunking: {
    targetTokenSize: number;
    maxTokenSize: number;
    overlapTokens: number;
    preserveCodeBlocks: boolean;
  };
  quality: {
    minContentLength: number;
    requireHeaders: boolean;
    validateLinks: boolean;
  };
  relationships: {
    enableCrossReferences: boolean;
    similarityThreshold: number;
    maxRelatedChunks: number;
  };
}
```

**Next Steps**: Proceed to Day 5 Vector Database Setup Evaluation
