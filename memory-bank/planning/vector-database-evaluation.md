# Vector Database Evaluation & Setup
*Generated: Day 5 of Knowledge Base Ingestion Process Design*

## 🎯 **Requirements Analysis**

### **AIGuardian Ecosystem Requirements**
Based on the existing tech stack and infrastructure:

```typescript
interface VectorDBRequirements {
  // Performance
  maxDocuments: 50000;           // Future growth capacity
  maxVectorDimensions: 1536;     // OpenAI text-embedding-ada-002
  queryLatency: '<100ms';        // P95 response time
  throughput: '1000+ QPS';       // Concurrent user support
  
  // Integration
  deployment: 'kubernetes' | 'docker' | 'cloud-managed';
  authentication: 'jwt-compatible';
  monitoring: 'prometheus-metrics';
  logging: 'structured-json';
  
  // Data Management
  backupRestore: true;
  versioning: true;
  multiTenancy: true;           // Tenant isolation
  encryption: 'at-rest-and-transit';
  
  // Development
  localDevelopment: true;
  cicdIntegration: true;
  apiFirst: true;
  typeScriptSupport: true;
}
```

### **Current AIGuardian Infrastructure Context**
- **Container Platform**: Docker/Podman + Kubernetes
- **Database**: PostgreSQL (existing pattern)
- **Authentication**: UAM service with JWT
- **Monitoring**: Prometheus + Grafana
- **Logging**: Pino structured logging
- **Language**: TypeScript/Node.js (NestJS)

## 📊 **Vector Database Options Evaluation**

### **Option 1: Pinecone (Cloud-Managed)**

#### **Pros** ✅
- **Fully Managed**: No infrastructure overhead
- **High Performance**: Sub-100ms query latency
- **Scalability**: Auto-scaling to millions of vectors
- **Enterprise Features**: Multi-tenancy, security, monitoring
- **Developer Experience**: Excellent TypeScript SDK
- **Reliability**: 99.9% uptime SLA

#### **Cons** ❌
- **Cost**: $70+/month for production workloads
- **Vendor Lock-in**: Proprietary cloud service
- **Data Sovereignty**: Data stored in US/EU regions
- **Limited Control**: Cannot customize infrastructure
- **Network Dependency**: Requires internet connectivity

#### **Technical Fit** 🔧
```typescript
// Pinecone Integration Example
import { PineconeClient } from '@pinecone-database/pinecone';

const pinecone = new PineconeClient();
await pinecone.init({
  environment: 'us-west1-gcp',
  apiKey: process.env.PINECONE_API_KEY,
});

const index = pinecone.Index('aiguardian-knowledge-base');
```

**AIGuardian Compatibility**: ⭐⭐⭐⭐ (Good fit, but external dependency)

---

### **Option 2: Weaviate (Self-Hosted/Cloud)**

#### **Pros** ✅
- **Flexible Deployment**: Self-hosted or cloud options
- **Rich Features**: Built-in ML models, GraphQL API
- **Open Source**: No vendor lock-in
- **Multi-Modal**: Text, images, and other data types
- **Strong Community**: Active development and support
- **Kubernetes Native**: Excellent K8s integration

#### **Cons** ❌
- **Complexity**: Requires infrastructure management
- **Resource Heavy**: Higher memory/CPU requirements
- **Learning Curve**: More complex than simple vector stores
- **Maintenance**: Need to manage updates and scaling

#### **Technical Fit** 🔧
```typescript
// Weaviate Integration Example
import weaviate from 'weaviate-ts-client';

const client = weaviate.client({
  scheme: 'http',
  host: 'localhost:8080',
});

const result = await client.graphql
  .get()
  .withClassName('Document')
  .withFields('content title metadata')
  .withNearText({ concepts: ['litmus testing'] })
  .withLimit(10)
  .do();
```

**AIGuardian Compatibility**: ⭐⭐⭐⭐⭐ (Excellent fit for K8s environment)

---

### **Option 3: Chroma (Open Source)**

#### **Pros** ✅
- **Lightweight**: Minimal resource requirements
- **Simple**: Easy to set up and use
- **Open Source**: No licensing costs
- **Python/JS Support**: Good language support
- **Local Development**: Excellent for development
- **Fast Iteration**: Quick to prototype and test

#### **Cons** ❌
- **Limited Scale**: Not designed for large production workloads
- **Basic Features**: Fewer enterprise features
- **Young Project**: Less mature than alternatives
- **Limited Monitoring**: Basic observability features
- **Single Node**: Limited clustering capabilities

#### **Technical Fit** 🔧
```typescript
// Chroma Integration Example
import { ChromaClient } from 'chromadb';

const client = new ChromaClient();
const collection = await client.createCollection({
  name: 'aiguardian-docs',
  metadata: { description: 'AIGuardian knowledge base' }
});

await collection.add({
  documents: ['Litmus is a testing platform...'],
  metadatas: [{ source: 'Litmus-Overview.md' }],
  ids: ['doc1']
});
```

**AIGuardian Compatibility**: ⭐⭐⭐ (Good for development, limited for production)

---

### **Option 4: PostgreSQL + pgvector (Extension)**

#### **Pros** ✅
- **Existing Infrastructure**: Leverages current PostgreSQL setup
- **Unified Database**: Same database for relational and vector data
- **Cost Effective**: No additional licensing
- **Familiar Operations**: Existing PostgreSQL expertise
- **ACID Compliance**: Full transactional support
- **Backup/Recovery**: Existing PostgreSQL procedures

#### **Cons** ❌
- **Performance**: Slower than specialized vector databases
- **Limited Features**: Basic vector operations only
- **Scaling Challenges**: PostgreSQL scaling limitations
- **Memory Usage**: Higher memory requirements for large datasets
- **Query Complexity**: More complex queries for advanced operations

#### **Technical Fit** 🔧
```sql
-- pgvector Setup
CREATE EXTENSION vector;

CREATE TABLE document_embeddings (
  id UUID PRIMARY KEY,
  content TEXT,
  embedding vector(1536),
  metadata JSONB
);

CREATE INDEX ON document_embeddings 
USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);
```

**AIGuardian Compatibility**: ⭐⭐⭐⭐ (Good fit, leverages existing infrastructure)

## 🏆 **Recommendation: Weaviate**

### **Decision Matrix**

| Criteria | Pinecone | Weaviate | Chroma | pgvector | Weight |
|----------|----------|----------|---------|----------|---------|
| **Performance** | 5 | 4 | 3 | 3 | 25% |
| **Infrastructure Fit** | 3 | 5 | 4 | 5 | 20% |
| **Cost** | 2 | 4 | 5 | 5 | 15% |
| **Scalability** | 5 | 4 | 2 | 3 | 15% |
| **Features** | 4 | 5 | 3 | 2 | 10% |
| **Maintenance** | 5 | 3 | 4 | 4 | 10% |
| **Data Sovereignty** | 2 | 5 | 5 | 5 | 5% |

**Weighted Scores**:
- **Weaviate**: 4.25/5 🏆
- **Pinecone**: 3.85/5
- **pgvector**: 3.80/5  
- **Chroma**: 3.35/5

### **Why Weaviate?**

1. **Perfect Infrastructure Fit**: Kubernetes-native, aligns with AIGuardian deployment
2. **Balanced Performance**: Excellent performance without external dependencies
3. **Feature Rich**: Advanced querying, filtering, and multi-modal capabilities
4. **Data Control**: Full control over data location and security
5. **Future Proof**: Can handle growth and additional use cases
6. **Cost Effective**: No per-query pricing, predictable costs

## 🔧 **Weaviate Implementation Plan**

### **Phase 1: Development Setup**

#### **Docker Compose for Local Development**
```yaml
# docker-compose.weaviate.yml
version: '3.8'
services:
  weaviate:
    image: semitechnologies/weaviate:1.22.4
    ports:
      - "8080:8080"
    environment:
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      DEFAULT_VECTORIZER_MODULE: 'none'
      ENABLE_MODULES: 'text2vec-openai'
      CLUSTER_HOSTNAME: 'node1'
    volumes:
      - weaviate_data:/var/lib/weaviate

volumes:
  weaviate_data:
```

#### **TypeScript Integration**
```typescript
// src/services/vector-database.service.ts
import weaviate, { WeaviateClient } from 'weaviate-ts-client';

@Injectable()
export class VectorDatabaseService {
  private client: WeaviateClient;

  constructor() {
    this.client = weaviate.client({
      scheme: process.env.WEAVIATE_SCHEME || 'http',
      host: process.env.WEAVIATE_HOST || 'localhost:8080',
      apiKey: new weaviate.ApiKey(process.env.WEAVIATE_API_KEY || ''),
    });
  }

  async createSchema() {
    const schema = {
      class: 'Document',
      vectorizer: 'none', // We'll provide embeddings
      properties: [
        {
          name: 'content',
          dataType: ['text'],
        },
        {
          name: 'title',
          dataType: ['string'],
        },
        {
          name: 'metadata',
          dataType: ['object'],
        },
      ],
    };

    await this.client.schema.classCreator().withClass(schema).do();
  }
}
```

### **Phase 2: Production Deployment**

#### **Kubernetes Deployment**
```yaml
# k8s/weaviate-deployment.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: weaviate
  namespace: aiguardian
spec:
  replicas: 2
  selector:
    matchLabels:
      app: weaviate
  template:
    metadata:
      labels:
        app: weaviate
    spec:
      containers:
      - name: weaviate
        image: semitechnologies/weaviate:1.22.4
        ports:
        - containerPort: 8080
        env:
        - name: PERSISTENCE_DATA_PATH
          value: '/var/lib/weaviate'
        - name: AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED
          value: 'false'
        - name: AUTHENTICATION_APIKEY_ENABLED
          value: 'true'
        - name: AUTHENTICATION_APIKEY_ALLOWED_KEYS
          valueFrom:
            secretKeyRef:
              name: weaviate-secrets
              key: api-keys
        volumeMounts:
        - name: weaviate-storage
          mountPath: /var/lib/weaviate
      volumes:
      - name: weaviate-storage
        persistentVolumeClaim:
          claimName: weaviate-pvc
```

### **Phase 3: Schema Design**

#### **Document Schema**
```typescript
interface DocumentSchema {
  class: 'Document';
  properties: {
    content: string;           // Main document content
    title: string;            // Document title
    sourceFile: string;       // Original filename
    category: string;         // Content category
    product: string;          // Product association
    audience: string;         // Target audience
    priority: number;         // Search priority
    wordCount: number;        // Content length
    lastModified: string;     // ISO date string
    metadata: {
      hasCodeExamples: boolean;
      hasImages: boolean;
      relatedFiles: string[];
      keywords: string[];
    };
  };
}
```

#### **Chunk Schema**
```typescript
interface ChunkSchema {
  class: 'Chunk';
  properties: {
    content: string;          // Chunk content
    title: string;           // Section title
    context: string;         // Surrounding context
    documentId: string;      // Parent document reference
    chunkType: string;       // Type of chunk
    hierarchyPath: string[]; // Header path
    tokenCount: number;      // Estimated tokens
    startLine: number;       // Source line start
    endLine: number;         // Source line end
    keywords: string[];      // Search keywords
  };
}
```

## 📈 **Performance Optimization**

### **Indexing Strategy**
- **HNSW Index**: For fast approximate nearest neighbor search
- **Inverted Index**: For keyword-based filtering
- **Property Indexes**: For metadata filtering

### **Query Optimization**
```typescript
// Optimized search query
const searchResults = await client.graphql
  .get()
  .withClassName('Chunk')
  .withFields('content title documentId metadata')
  .withNearText({ 
    concepts: [query],
    certainty: 0.7 
  })
  .withWhere({
    operator: 'And',
    operands: [
      {
        path: ['category'],
        operator: 'Equal',
        valueString: userContext.preferredCategory
      },
      {
        path: ['priority'],
        operator: 'GreaterThan',
        valueNumber: 3
      }
    ]
  })
  .withLimit(10)
  .do();
```

### **Monitoring & Observability**
- **Prometheus Metrics**: Query latency, throughput, error rates
- **Health Checks**: Database connectivity and performance
- **Logging**: Structured logs for query analysis
- **Alerting**: Performance degradation and error thresholds

**Next Steps**: Complete Week 1 and proceed to Week 2 Implementation
