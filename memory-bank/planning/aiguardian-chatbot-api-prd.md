# Product Requirements Document: AIGuardian Chatbot API

## Executive Summary

### Product Vision
Create an intelligent AI chatbot API that serves as the primary support interface for AIGuardian ecosystem users, providing instant, accurate responses to queries about Litmus, Sentinel, and AIGuardian platform capabilities.

### Business Objectives
- **Reduce Support Load**: Decrease repetitive support queries by 60%
- **Improve Response Time**: Enable L1/L2 support teams to provide faster email responses
- **Enhance User Experience**: Provide 24/7 instant access to product information
- **Knowledge Centralization**: Create a single source of truth for product information

## Product Overview

### Target Users
1. **Primary Users**:
   - External tenant representatives seeking product information
   - Customer support teams (L1, L2) handling user queries
   - Internal developers (L3) needing quick reference

2. **Use Cases**:
   - Product overview and capability questions
   - Onboarding and integration guidance
   - Technical implementation support
   - Troubleshooting assistance

### Core Value Proposition
An API-first chatbot that leverages AIGuardian's comprehensive documentation to provide accurate, contextual responses about Litmus testing capabilities, Sentinel guardrails, and platform integration patterns.

## Functional Requirements

### 1. Core API Functionality

#### 1.1 Chat Endpoint
```
POST /api/v1/chat
Content-Type: application/json
Authorization: Bearer <token>
x-tenant-id: <tenant-id>

{
  "message": "What is Litmus?",
  "context": {
    "session_id": "uuid",
    "user_role": "tenant_user|support_l1|support_l2|developer",
    "tenant_id": "uuid"
  }
}

Response:
{
  "response": "Litmus is AIGuardian's comprehensive AI safety testing platform...",
  "sources": [
    {
      "title": "Litmus Overview",
      "url": "/docs/litmus-overview",
      "relevance_score": 0.95
    }
  ],
  "session_id": "uuid",
  "response_id": "uuid",
  "confidence_score": 0.92
}
```

#### 1.2 Session Management
```
GET /api/v1/sessions/{session_id}
POST /api/v1/sessions
DELETE /api/v1/sessions/{session_id}
```

### 2. Knowledge Base Integration

#### 2.1 Documentation Sources
- **Primary**: `/apps/aiguardian-web/docs/wiki/` (40+ documentation files)
- **Secondary**: `/memory-bank/` (implementation notes, troubleshooting)
- **API Documentation**: Swagger/OpenAPI specs for Litmus and Sentinel APIs

#### 2.2 Content Categories
1. **Product Overviews**: Litmus, Sentinel, AIGuardian platform
2. **Onboarding Guides**: Step-by-step integration instructions
3. **Technical References**: API usage, configuration examples
4. **Troubleshooting**: Common issues and solutions
5. **Architecture**: System design and component relationships

### 3. Query Processing Capabilities

#### 3.1 Supported Query Types
- **Product Information**: "What is Sentinel?", "Litmus capabilities"
- **Integration Help**: "How to integrate Litmus?", "Sentinel API examples"
- **Technical Questions**: "Prompt repetition in Litmus runs", "Threshold settings"
- **Environment Access**: "Playground availability", "Testing environments"
- **Configuration**: "Guardrail categories", "API key setup"

#### 3.2 Response Features
- **Contextual Answers**: Tailored to user role and tenant context
- **Source Attribution**: Links to relevant documentation
- **Code Examples**: When applicable for technical queries
- **Follow-up Suggestions**: Related questions or next steps

## Technical Requirements

### 1. Architecture

```mermaid
graph TD
    A[Client Applications] --> B[Chatbot API Gateway]
    B --> C[Authentication Service]
    B --> D[Chat Processing Service]
    D --> E[Knowledge Base Service]
    D --> F[AI/LLM Service]
    E --> G[Vector Database]
    E --> H[Document Store]
    F --> I[External LLM Provider]
    D --> J[Session Store]
    D --> K[Analytics Service]
```

### 2. Technology Stack

#### 2.1 Backend Framework
- **Framework**: NestJS (TypeScript) - consistent with AIGuardian ecosystem
- **Database**: PostgreSQL for session/analytics data
- **Vector Store**: Pinecone/Weaviate for document embeddings
- **Cache**: Redis for session management and response caching

#### 2.2 AI/ML Components
- **LLM Provider**: OpenAI GPT-4 or Azure OpenAI
- **Embedding Model**: text-embedding-ada-002 or similar
- **RAG Framework**: LangChain or custom implementation

### 3. Integration Points

#### 3.1 AIGuardian Ecosystem
- **Authentication**: UAM service integration (JWT + x-tenant-id)
- **Permissions**: Role-based access to different information levels
- **Logging**: Consistent with AIGuardian logging patterns (Pino)
- **Monitoring**: Integration with existing observability stack

#### 3.2 External Services
- **LLM API**: OpenAI/Azure OpenAI for response generation
- **Vector Database**: For semantic search capabilities
- **Analytics**: Usage tracking and performance metrics

## Implementation Phases

### Phase 1: MVP API (4-6 weeks)
**Scope**: Basic chat functionality with static knowledge base

**Deliverables**:
- Core chat API endpoint
- Document ingestion pipeline for wiki content
- Basic RAG implementation
- Authentication integration
- Simple response generation

**Success Criteria**:
- API responds to basic product questions
- Integration with UAM authentication
- 80% accuracy on predefined test queries

### Phase 2: Enhanced Intelligence (3-4 weeks)
**Scope**: Improved AI capabilities and session management

**Deliverables**:
- Session management and context retention
- Advanced query understanding
- Source attribution and confidence scoring
- Response quality improvements
- Basic analytics dashboard

**Success Criteria**:
- Multi-turn conversations
- 90% accuracy on test queries
- Response time < 3 seconds

### Phase 3: Production Readiness (2-3 weeks)
**Scope**: Performance optimization and monitoring

**Deliverables**:
- Performance optimization
- Comprehensive monitoring and alerting
- Rate limiting and security hardening
- Documentation and deployment guides
- Load testing and scaling

**Success Criteria**:
- Production-ready deployment
- 99.9% uptime SLA
- Support for 100+ concurrent users

## Success Metrics

### 1. Performance Metrics
- **Response Time**: < 3 seconds for 95% of queries
- **Accuracy**: > 90% correct responses on test dataset
- **Availability**: 99.9% uptime
- **Throughput**: Support 100+ concurrent users

### 2. Business Metrics
- **Support Ticket Reduction**: 60% decrease in repetitive queries
- **User Satisfaction**: > 4.5/5 rating
- **Adoption Rate**: 80% of active tenants use chatbot monthly
- **Resolution Rate**: 85% of queries resolved without escalation

### 3. Quality Metrics
- **Confidence Score**: Average > 0.8 for responses
- **Source Attribution**: 95% of responses include relevant sources
- **False Positive Rate**: < 5% incorrect responses
- **Coverage**: 90% of documentation content accessible

## Risk Assessment

### 1. Technical Risks
- **LLM Hallucination**: Mitigation through RAG and confidence scoring
- **Performance**: Caching and optimization strategies
- **Integration Complexity**: Phased approach with UAM integration

### 2. Business Risks
- **User Adoption**: Comprehensive testing with support teams
- **Content Quality**: Regular knowledge base updates and validation
- **Cost Management**: Usage monitoring and rate limiting

## Future Enhancements

### Phase 4: Advanced Features (Future)
- **Multi-language Support**: Support for additional languages
- **Voice Interface**: Speech-to-text and text-to-speech capabilities
- **Proactive Assistance**: Contextual suggestions based on user behavior
- **Integration Expansion**: Direct integration with Jira for ticket creation
- **Advanced Analytics**: User journey analysis and content gap identification

## User Stories

### Epic 1: Basic Query Handling
**As a** tenant user
**I want to** ask questions about AIGuardian products
**So that** I can understand capabilities and integration options

**Acceptance Criteria**:
- System responds to "What is Litmus?" with comprehensive overview
- System responds to "What is Sentinel?" with guardrail information
- Responses include relevant documentation links
- Response time < 3 seconds

### Epic 2: Technical Integration Support
**As a** developer
**I want to** get specific integration guidance
**So that** I can implement Litmus/Sentinel in my application

**Acceptance Criteria**:
- System provides code examples for API integration
- System explains configuration options
- System suggests best practices for implementation
- System handles follow-up technical questions

### Epic 3: Support Team Assistance
**As a** L1/L2 support team member
**I want to** quickly find answers to customer questions
**So that** I can provide faster email responses

**Acceptance Criteria**:
- System maintains conversation context
- System provides confidence scores for responses
- System escalates complex queries appropriately
- System tracks query patterns for improvement

## Detailed Technical Specifications

### 1. Database Schema

```sql
-- Sessions table
CREATE TABLE chatbot_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    user_id UUID,
    user_role VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL,
    metadata JSONB
);

-- Messages table
CREATE TABLE chatbot_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES chatbot_sessions(id),
    message_type VARCHAR(20) NOT NULL, -- 'user' or 'assistant'
    content TEXT NOT NULL,
    sources JSONB,
    confidence_score DECIMAL(3,2),
    processing_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Analytics table
CREATE TABLE chatbot_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    query_category VARCHAR(100),
    resolved BOOLEAN DEFAULT FALSE,
    user_feedback INTEGER, -- 1-5 rating
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 2. API Endpoints Specification

#### 2.1 Authentication
All endpoints require:
- `Authorization: Bearer <jwt_token>`
- `x-tenant-id: <tenant_uuid>`

#### 2.2 Core Endpoints

```typescript
// Chat endpoint
POST /api/v1/chat
interface ChatRequest {
  message: string;
  context?: {
    session_id?: string;
    user_role: 'tenant_user' | 'support_l1' | 'support_l2' | 'developer';
    tenant_id: string;
  };
}

interface ChatResponse {
  response: string;
  sources: Array<{
    title: string;
    url: string;
    relevance_score: number;
  }>;
  session_id: string;
  response_id: string;
  confidence_score: number;
  processing_time_ms: number;
}

// Session management
GET /api/v1/sessions/{session_id}
POST /api/v1/sessions
DELETE /api/v1/sessions/{session_id}

// Feedback
POST /api/v1/feedback
interface FeedbackRequest {
  response_id: string;
  rating: number; // 1-5
  comment?: string;
}

// Health check
GET /api/v1/health
```

### 3. Knowledge Base Processing

#### 3.1 Document Ingestion Pipeline
1. **Source Scanning**: Monitor `/apps/aiguardian-web/docs/wiki/` for changes
2. **Content Extraction**: Parse Markdown, extract metadata
3. **Chunking**: Split documents into semantic chunks (500-1000 tokens)
4. **Embedding Generation**: Create vector embeddings for each chunk
5. **Storage**: Store in vector database with metadata

#### 3.2 Content Categories and Tagging
```typescript
interface DocumentMetadata {
  category: 'product_overview' | 'onboarding' | 'technical' | 'troubleshooting' | 'api_reference';
  product: 'litmus' | 'sentinel' | 'aiguardian' | 'moonshot';
  audience: 'beginner' | 'intermediate' | 'advanced';
  last_updated: Date;
  source_file: string;
}
```

## Sample Queries and Expected Responses

### Query 1: Product Overview
**Input**: "What is Litmus?"
**Expected Response**:
"Litmus is AIGuardian's comprehensive AI safety testing platform designed for government applications. It provides baseline security and safety testing with NAIG-sanctioned test suites, automated test execution, and CI/CD pipeline integration. Key capabilities include custom test scenarios for government use cases and real-time risk assessment reporting."
**Sources**: Litmus Overview, Litmus Getting Started
**Confidence**: 0.95

### Query 2: Technical Integration
**Input**: "How do I integrate Litmus into my CI/CD pipeline?"
**Expected Response**:
"To integrate Litmus into your CI/CD pipeline: 1) Set up API credentials through the UAM system, 2) Configure your pipeline to call the Litmus API endpoints, 3) Define test suites and parameters, 4) Set up result processing and reporting. Here's a sample configuration..."
**Sources**: Litmus API Documentation, CI/CD Integration Guide
**Confidence**: 0.88

### Query 3: Environment Access
**Input**: "Is there a playground environment for Litmus?"
**Expected Response**:
"Yes, Litmus provides a playground environment for testing. You can access it through the Litmus web interface after tenant onboarding. The playground allows you to run ad-hoc tests and experiment with different configurations before implementing in production."
**Sources**: Litmus Playground Guide, Environment Setup
**Confidence**: 0.92

## Deployment and Operations

### 1. Infrastructure Requirements
- **Compute**: 2-4 CPU cores, 8-16GB RAM for API service
- **Storage**: PostgreSQL database, Vector database storage
- **Network**: Load balancer, SSL termination
- **Monitoring**: Prometheus metrics, structured logging

### 2. Security Considerations
- **Authentication**: JWT token validation through UAM service
- **Authorization**: Role-based access control
- **Data Privacy**: No storage of sensitive conversation content
- **Rate Limiting**: Per-tenant and per-user limits
- **Input Validation**: Sanitization of user inputs

### 3. Monitoring and Alerting
- **Performance Metrics**: Response time, throughput, error rates
- **Business Metrics**: Query resolution rate, user satisfaction
- **System Health**: Database connectivity, LLM API availability
- **Cost Monitoring**: LLM API usage and costs

This comprehensive PRD provides the foundation for building an intelligent chatbot API that will significantly enhance the AIGuardian user experience and reduce support overhead.
