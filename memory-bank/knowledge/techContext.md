# Technical Context

## Technologies Used

### Primary Languages and Frameworks
- TypeScript / JavaScript (Node.js)
- Next.js for frontend
- Express.js for backend APIs
- Python for Sentinel API

### Database
- PostgreSQL 14.6 as the primary database
- Prisma ORM for database access
- Separate databases for UAM, Litmus, and Sentinel services
- Generic database scripts in `scripts/postgres/` for initialization and testing
  - `init-db.sql`: Parameterized database initialization script
  - `clean-db.sql`: Parameterized database cleanup script
  - `local-setup.sh`: Local development setup script
  - `ci-setup.sh`, `ci-cleanup.sh`: CI pipeline scripts

### Containerization and Deployment
- Docker/<PERSON>dman for local development
- Kubernetes for SIT and Production deployment
- AWS for cloud infrastructure

### Testing
- Jest for unit testing
- Cypress for E2E testing
- Local testing with Podman containers
- SIT testing with dedicated scripts for database management
  - `scripts/postgres/ci-setup.sh` - Setup databases for CI pipeline
  - `scripts/postgres/ci-cleanup.sh` - Cleanup databases after CI pipeline

### Security
- AWS Secrets Manager for credentials in SIT/Production
- Password parameterization in database scripts
- JWT for authentication
- Role-based access control

## Development Setup

### Local Development
1. Clone the repository
2. Install dependencies with `pnpm install`
3. Start database container with `podman compose up -d database`
4. Initialize databases with `./scripts/postgres/local-setup.sh`
5. Set up environment variables in `.env` files
6. Start services with `pnpm dev`

### Database Connection
- Local: `postgres://<user>:<password>@localhost:5432/<database>`
- SIT: Access via Kubernetes port-forwarding
  ```bash
  kubectl port-forward svc/<service> <local-port>:5432 -n <namespace>
  ```

### Key Commands
- Initialize all databases: `./scripts/postgres/local-setup.sh`
- Test individual database setup: `./scripts/postgres/<service>/test-local-setup.sh`
- Access PostgreSQL CLI: `podman exec -it ai-database-1 psql -U postgres`
- Seed UAM database: `pnpm --filter uam-db db:seed`

## Technical Constraints

### Environment-Specific Configurations
- Development: Local Podman containers
- SIT: Kubernetes cluster with port forwarding
- Production: AWS managed services with enhanced security

### Database Architecture
- Each service has its own database for isolation
- UAM database holds user and permission data
- Litmus database stores analysis results
- Sentinel database manages security policies
- All databases follow the same initialization and cleanup pattern

### Security Requirements
- No hardcoded credentials in scripts or code
- Database passwords must be parameterized
- Principle of least privilege for database users
- Proper cleanup of test databases between runs

## Dependencies

### Database Dependencies
- PostgreSQL 14.6
- Required extensions: `uuid-ossp`
- Prisma ORM for database access
- Connection strings stored in environment variables or AWS Secrets Manager

### Service Dependencies
- Each service requires its own database
- UAM service provides authentication for other services
- Services communicate via RESTful APIs
- Environment-specific configuration