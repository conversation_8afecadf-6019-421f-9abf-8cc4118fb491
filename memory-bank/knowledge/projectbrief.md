# Project Brief: AI Guardian

## Overview
AI Guardian is a comprehensive platform for managing and monitoring AI models and applications. The project consists of several interconnected components including UAM (User Access Management), Litmus, Sentinel, and Moonshot services that work together to provide a secure, monitored AI ecosystem.

## Core Objectives
- Provide robust user access management for AI services
- Enable monitoring and testing of AI models
- Support secure API access to AI capabilities
- Facilitate deployment in various environments (local, SIT, production)

## Key Components
1. **UAM (User Access Management)**: Handles authentication, authorization, and user permissions
2. **Litmus**: Testing and evaluation framework for AI models 
3. **Sentinel**: Monitoring and security service for AI applications
4. **Moonshot**: Core AI service implementation

## Current Focus
Implementing database management and seeding capabilities for the SIT (System Integration Testing) environment, focusing initially on the UAM database component.

## Technical Stack
- Database: PostgreSQL
- Containerization: Docker/Podman
- Orchestration: Kubernetes
- Backend: Node.js, Python
- Frontend: Next.js, React
- ORM: Prisma

## Project Organization
- **<PERSON>ra Tickets**: Stored in `/out/sprint-{sprint-number}/tickets/` as Markdown files with naming format `AIGUARDIAN-{ticket-number} {ticket-title}.md`