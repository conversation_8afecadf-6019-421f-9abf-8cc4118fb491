# System Patterns

## Architecture Overview
The AI Guardian platform uses a microservices architecture with several specialized components:

```mermaid
graph TD
    Client[Client Applications] --> UAMS[UAM Service]
    Client --> LS[Litmus Service]
    Client --> SS[Sentinel Service]
    Client --> MS[Moonshot Service]
    
    UAMS --> UAMDB[(UAM Database)]
    LS --> LDB[(Litmus Database)]
    SS --> SDB[(Sentinel Database)]
    MS --> MDB[(Moonshot Data)]
    
    AWS[AWS Services] --- UAMS
    AWS --- SS
```

## Database Management Patterns

### PostgreSQL Service Pattern
PostgreSQL databases are used across multiple services with a consistent pattern:
- Each service has its own database (UAM, Litmus, Sentinel)
- Users with specific permissions for each database
- Each user has CREATEDB privileges for Prisma shadow database functionality
- Consistent initialization and cleanup scripts
- Common extensions like uuid-ossp

### Database Seeding Pattern
For consistent testing environments, we follow these patterns:
1. **Clean -> Initialize -> Seed sequence**:
   - Clean up removes existing database and users
   - Initialize creates fresh database, users, and permissions (including CREATEDB)
   - Seed populates with standard test data

2. **Environment-specific configuration**:
   - Local environment: Direct container access
   - SIT environment: Kubernetes port forwarding
   - Production: Restricted access patterns

3. **Credential Management**:
   - Development: Fixed credentials in .env files
   - SIT: AWS Secrets Manager for secure storage
   - Production: AWS Secrets Manager with stricter access controls

## Data Management Patterns

### Prisma ORM Usage
- Schema definitions in .prisma files
- Generated clients for type-safe database access
- Seeding via Prisma's seeding mechanism
- Environment configuration through .env files

### Testing Data Management
- Reset database before tests for consistent state
- Seed with known test data
- Use connection pooling for efficient database connections
- Transaction isolation for test independence

## Security Patterns

### Access Control
- Service-specific database users
- Limited permissions based on service needs
- Isolation between service databases
- No direct production database access

### Credential Management
- No hardcoded credentials in scripts
- Secrets manager for sensitive information
- Dynamic credential generation for ephemeral environments
- Separate credentials for different environments