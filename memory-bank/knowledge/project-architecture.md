# AI Guardian Project Architecture

## Overview
The AI Guardian project is a comprehensive microservices platform designed for managing, testing, and securing AI applications in Singapore's government infrastructure. The platform integrates multiple specialized services to provide end-to-end AI governance capabilities.

## High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Applications]
        CLI[CLI Interfaces]
        API_CLIENTS[API Clients]
    end
    
    subgraph "Gateway & Authentication"
        UAM[UAM Service<br/>User Access Management]
        AUTH[Authentication Gateway]
    end
    
    subgraph "Core AI Services"
        LITMUS[Litmus Service<br/>AI Testing Platform]
        SENTINEL[Sentinel Service<br/>AI Guardrails]
        MOONSHOT[Moonshot Service<br/>LLM Evaluation]
    end
    
    subgraph "Web Applications"
        LITMUS_WEB[Litmus Web<br/>Next.js Frontend]
        SENTINEL_WEB[Sentinel Web<br/>Next.js Frontend]
        AIGUARDIAN_WEB[AI Guardian Web<br/>Docusaurus Docs]
    end
    
    subgraph "Data Layer"
        UAM_DB[(UAM Database<br/>PostgreSQL)]
        LITMUS_DB[(Litmus Database<br/>PostgreSQL)]
        SENTINEL_DB[(Sentinel Database<br/>PostgreSQL)]
        MOONSHOT_DATA[(Moonshot Data<br/>File Storage)]
    end
    
    subgraph "Infrastructure"
        AWS[AWS Services]
        K8S[Kubernetes Clusters]
        DOCKER[Docker Containers]
    end
    
    subgraph "Development & Testing"
        E2E[E2E Test Suites]
        CI_CD[CI/CD Pipelines]
        LOCAL_DEV[Local Development]
    end
    
    WEB --> AUTH
    CLI --> AUTH
    API_CLIENTS --> AUTH
    
    AUTH --> UAM
    UAM --> UAM_DB
    
    UAM --> LITMUS
    UAM --> SENTINEL
    UAM --> MOONSHOT
    
    LITMUS --> LITMUS_DB
    SENTINEL --> SENTINEL_DB
    MOONSHOT --> MOONSHOT_DATA
    
    LITMUS_WEB --> LITMUS
    SENTINEL_WEB --> SENTINEL
    
    LITMUS --> AWS
    SENTINEL --> AWS
    UAM --> AWS
    
    K8S --> LITMUS
    K8S --> SENTINEL
    K8S --> UAM
    
    E2E --> LITMUS
    E2E --> SENTINEL
    CI_CD --> K8S
```

## Legacy Projects Analysis

### Moonshot (Legacy Project 1)
**Purpose**: LLM Evaluation and Red-Teaming Framework
- **Technology**: Python-based application with web UI
- **Key Features**:
  - Benchmarking LLMs with multiple model providers
  - Red-teaming capabilities for adversarial testing
  - Automated attack modules for vulnerability detection
  - Support for OpenAI, Anthropic, Together, HuggingFace
- **Architecture Pattern**: Standalone application with CLI, Web UI, and API interfaces
- **Data Management**: File-based storage for datasets and evaluation results

### Moonshot1 (Legacy Project 2)
**Purpose**: Simplified deployment wrapper for Moonshot
- **Technology**: Minimal Node.js deployment configuration
- **Key Features**:
  - Deployment automation scripts
  - Kubernetes helm chart integration
- **Architecture Pattern**: Deployment-focused microservice
- **Integration**: Works as deployment layer for main Moonshot service

## Modern AI Guardian Architecture

### Service Breakdown

#### 1. User Access Management (UAM)
```mermaid
graph LR
    UAM_API[UAM API<br/>NestJS] --> UAM_DB[(PostgreSQL)]
    JWT[JWT Tokens] --> UAM_API
    TENANT[Tenant Management] --> UAM_API
```
- **Technology**: NestJS (TypeScript)
- **Responsibilities**: Authentication, authorization, multi-tenancy
- **Database**: PostgreSQL with Prisma ORM
- **Key Features**: JWT token exchange, role-based access control

#### 2. Litmus Testing Platform
```mermaid
graph LR
    LITMUS_API[Litmus API<br/>NestJS] --> LITMUS_DB[(PostgreSQL)]
    LITMUS_WEB[Litmus Web<br/>Next.js] --> LITMUS_API
    TEST_SUITES[Test Suites] --> LITMUS_API
    CI_CD_INTEGRATION[CI/CD Integration] --> LITMUS_API
```
- **Technology**: NestJS backend, Next.js frontend
- **Responsibilities**: AI safety testing, automated test execution
- **Key Features**: 
  - Comprehensive baseline security testing
  - CI/CD pipeline integration
  - Custom test scenarios
  - Real-time risk assessment

#### 3. Sentinel Guardrails Service
```mermaid
graph LR
    SENTINEL_API[Sentinel Core API<br/>NestJS] --> SENTINEL_DB[(PostgreSQL)]
    SENTINEL_WEB[Sentinel Web<br/>Next.js] --> SENTINEL_API
    GUARDRAILS[Input/Output Guardrails] --> SENTINEL_API
    PROTECTION[AI Risk Protection] --> SENTINEL_API
```
- **Technology**: NestJS backend, Next.js frontend
- **Responsibilities**: AI guardrails, risk mitigation
- **Key Features**:
  - Input/output filtering
  - Prompt injection detection
  - Toxicity prevention
  - PII leakage protection

#### 4. Moonshot Integration (Evolved)
```mermaid
graph LR
    MOONSHOT_CORE[Moonshot Core<br/>Python] --> MOONSHOT_DATA[(File Storage)]
    API_WRAPPER[API Wrapper] --> MOONSHOT_CORE
    WEB_INTERFACE[Web Interface] --> API_WRAPPER
    EVALUATIONS[LLM Evaluations] --> MOONSHOT_CORE
```
- **Technology**: Python core with API wrappers
- **Responsibilities**: LLM evaluation, red-teaming
- **Integration**: Embedded within AI Guardian ecosystem

## Data Architecture

### Database Design Pattern
```mermaid
graph TB
    subgraph "Database Isolation"
        UAM_SCHEMA[UAM Schema<br/>Users, Tenants, Roles]
        LITMUS_SCHEMA[Litmus Schema<br/>Tests, Results, Reports]
        SENTINEL_SCHEMA[Sentinel Schema<br/>Guardrails, Policies]
    end
    
    subgraph "Shared Infrastructure"
        POSTGRES[PostgreSQL 14.6]
        PRISMA[Prisma ORM]
        MIGRATIONS[Migration Scripts]
    end
    
    UAM_SCHEMA --> POSTGRES
    LITMUS_SCHEMA --> POSTGRES
    SENTINEL_SCHEMA --> POSTGRES
    
    PRISMA --> POSTGRES
    MIGRATIONS --> POSTGRES
```

### Security & Credential Management
```mermaid
graph LR
    LOCAL[Local Development<br/>.env files] --> DEV_DB[(Local PostgreSQL)]
    SIT[SIT Environment<br/>AWS Secrets] --> SIT_DB[(SIT PostgreSQL)]
    PROD[Production<br/>AWS Secrets] --> PROD_DB[(Production PostgreSQL)]
    
    AWS_SECRETS[AWS Secrets Manager] --> SIT
    AWS_SECRETS --> PROD
```

## Deployment Architecture

### Environment Patterns
```mermaid
graph TB
    subgraph "Local Development"
        PODMAN[Podman Containers]
        LOCAL_SCRIPTS[Local Setup Scripts]
    end
    
    subgraph "SIT Environment"
        K8S_SIT[Kubernetes SIT]
        AWS_SIT[AWS Infrastructure]
        PORT_FORWARD[Port Forwarding]
    end
    
    subgraph "Production"
        K8S_PROD[Kubernetes Production]
        AWS_PROD[AWS Production]
        LOAD_BALANCER[Load Balancers]
    end
    
    PODMAN --> LOCAL_SCRIPTS
    K8S_SIT --> AWS_SIT
    K8S_PROD --> AWS_PROD
```

## Technology Stack Summary

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Backend APIs** | NestJS (TypeScript) | UAM, Litmus, Sentinel services |
| **Frontend Apps** | Next.js (React) | Web interfaces for each service |
| **Legacy Core** | Python | Moonshot LLM evaluation engine |
| **Database** | PostgreSQL 14.6 | Primary data storage |
| **ORM** | Prisma | Database access and migrations |
| **Containerization** | Docker/Podman | Local development and deployment |
| **Orchestration** | Kubernetes | SIT and Production deployment |
| **Cloud Platform** | AWS | Infrastructure and services |
| **Documentation** | Docusaurus | AI Guardian website and docs |
| **Monorepo** | pnpm + Turbo | Workspace management |
| **Testing** | Jest, Playwright | Unit and E2E testing |

## Integration Patterns

### Inter-Service Communication
```mermaid
sequenceDiagram
    participant Client
    participant UAM
    participant Litmus
    participant Sentinel
    participant Database
    
    Client->>UAM: Authenticate
    UAM->>Database: Validate credentials
    UAM-->>Client: JWT Token + Tenant ID
    
    Client->>Litmus: API Request (with JWT)
    Litmus->>UAM: Validate token
    UAM-->>Litmus: User permissions
    Litmus->>Database: Execute operation
    Litmus-->>Client: Response
    
    Client->>Sentinel: Guardrail check
    Sentinel->>Database: Policy lookup
    Sentinel-->>Client: Protection result
```

### CI/CD Integration
```mermaid
graph LR
    CODE_COMMIT[Code Commit] --> CI_PIPELINE[CI Pipeline]
    CI_PIPELINE --> BUILD[Build & Test]
    BUILD --> DB_SETUP[Database Setup]
    DB_SETUP --> E2E_TESTS[E2E Tests]
    E2E_TESTS --> DEPLOY[Deploy to K8S]
    DEPLOY --> CLEANUP[Database Cleanup]
```

## Key Architectural Decisions

1. **Microservices Architecture**: Each AI capability (testing, guardrails, evaluation) is a separate service
2. **Database Isolation**: Each service has its own PostgreSQL database for data isolation
3. **Shared Authentication**: UAM service provides centralized authentication for all services
4. **Technology Consistency**: NestJS for all new backend services, Next.js for all frontend applications
5. **Legacy Integration**: Moonshot Python core integrated through API wrappers
6. **Environment Parity**: Consistent deployment patterns across local, SIT, and production
7. **Security First**: AWS Secrets Manager for credential management in cloud environments

## Evolution from Legacy

The project has evolved from two standalone Moonshot applications to a comprehensive microservices platform:

- **Legacy Moonshot** → **Integrated LLM Evaluation Service**
- **Legacy Moonshot1** → **Kubernetes Deployment Patterns**
- **New Services**: UAM, Litmus, Sentinel added for comprehensive AI governance
- **Unified Frontend**: Consistent Next.js applications with shared UI components
- **Centralized Documentation**: AI Guardian website with comprehensive product documentation

This architecture enables the Singapore government to have a complete AI governance platform that can handle authentication, testing, guardrails, and evaluation in a secure, scalable manner.
