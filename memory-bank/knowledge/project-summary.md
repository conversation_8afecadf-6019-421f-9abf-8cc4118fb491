# AI Guardian Project Summary

## Executive Summary

The AI Guardian project is Singapore's comprehensive AI governance platform designed specifically for government applications. It evolved from two legacy Moonshot projects into a modern microservices architecture that provides end-to-end AI safety, security, and evaluation capabilities for the public sector.

## Project Overview

**Mission**: Ensure responsible and safe use of Generative AI in Singapore's government services through comprehensive testing, monitoring, and guardrails.

**Vision**: Empower government agencies to confidently deploy AI applications while maintaining the highest standards of security, safety, and public trust.

## Legacy Foundation

### Original Moonshot Projects
1. **Moonshot (Legacy Project 1)**: LLM evaluation and red-teaming framework
   - Python-based application for testing language models
   - Benchmarking capabilities with major AI providers
   - Adversarial testing and vulnerability detection
   - Multi-interface support (CLI, Web UI, API)

2. **Moonshot1 (Legacy Project 2)**: Deployment automation
   - Kubernetes deployment wrapper
   - Helm chart integration
   - Infrastructure automation scripts

## Current AI Guardian Ecosystem

### Core Products

#### 1. **Litmus** - AI Testing Platform
**Purpose**: Comprehensive AI safety testing for government applications

**Key Capabilities**:
- Baseline security and safety testing with NAIG-sanctioned test suites
- Automated test execution and parallel processing
- CI/CD pipeline integration for continuous testing
- Custom test scenarios for government use cases
- Real-time risk assessment and reporting

**Technology Stack**:
- Backend: NestJS (TypeScript)
- Frontend: Next.js (React)
- Database: PostgreSQL
- Testing Framework: Custom test suite execution engine

**Target Users**: Government development teams, QA engineers, compliance teams

#### 2. **Sentinel** - AI Guardrails Service
**Purpose**: Robust protection for government AI systems

**Key Capabilities**:
- Input/output filtering for sensitive government data
- Prompt injection detection and prevention
- Toxicity detection in public communication channels
- PII leakage prevention for citizen data
- Multi-tenant SaaS platform with government compliance

**Technology Stack**:
- Backend: NestJS (TypeScript)
- Frontend: Next.js (React)
- Database: PostgreSQL
- AI Models: Custom guardrail models

**Target Users**: Government agencies deploying citizen-facing AI services

#### 3. **UAM (User Access Management)**
**Purpose**: Centralized authentication and authorization

**Key Capabilities**:
- Multi-tenant user management
- Role-based access control
- JWT token-based authentication
- Cross-service authorization
- Government security compliance

**Technology Stack**:
- Backend: NestJS (TypeScript)
- Database: PostgreSQL
- Authentication: JWT tokens

#### 4. **Moonshot Integration**
**Purpose**: Advanced LLM evaluation and red-teaming

**Key Capabilities**:
- Benchmarking with multiple model providers
- Automated red-teaming with research-backed techniques
- Custom model connector framework
- Comprehensive evaluation reporting

**Technology Stack**:
- Core: Python application
- Integration: API wrappers for ecosystem integration
- Data: File-based storage for evaluation results

## Technical Architecture

### Microservices Design
- **Service Isolation**: Each capability (testing, guardrails, evaluation, authentication) is a separate service
- **Database Per Service**: Independent PostgreSQL databases for data isolation
- **API-First**: RESTful APIs for all inter-service communication
- **Shared UI Components**: Consistent user experience across all applications

### Infrastructure
- **Local Development**: Podman containers with automated setup scripts
- **SIT Environment**: Kubernetes deployment with AWS integration
- **Production**: Kubernetes with AWS infrastructure and secrets management
- **CI/CD**: Automated testing and deployment pipelines

### Security Model
- **Authentication**: Centralized through UAM service
- **Authorization**: Role-based access control with tenant isolation
- **Secrets Management**: AWS Secrets Manager for cloud environments
- **Data Protection**: Encrypted communication and secure data handling

## Development Ecosystem

### Monorepo Structure
```
ai/
├── apps/                          # Application services
│   ├── litmus-api/               # Litmus backend service
│   ├── litmus-web/               # Litmus frontend application
│   ├── sentinel-api/             # Sentinel backend service
│   ├── sentinel-web/             # Sentinel frontend application
│   ├── uam-api/                  # UAM backend service
│   ├── moonshot/                 # Legacy Moonshot integration
│   └── aiguardian-web/           # Documentation website
├── packages/                      # Shared libraries
│   ├── aiguardian-ui/            # Shared UI components
│   ├── litmus-db/                # Litmus database package
│   ├── sentinel-db/              # Sentinel database package
│   ├── uam-db/                   # UAM database package
│   └── utils/                    # Shared utilities
├── memory-bank/                   # Project documentation
├── scripts/                       # Automation scripts
└── e2e/                          # End-to-end tests
```

### Technology Standards
- **Backend**: NestJS with TypeScript for all new services
- **Frontend**: Next.js with React and Tailwind CSS
- **Database**: PostgreSQL with Prisma ORM
- **Testing**: Jest for unit tests, Playwright for E2E
- **Documentation**: Docusaurus for public-facing docs
- **Package Management**: pnpm with Turbo for monorepo

## Current Development Status

### Completed Features ✅
- UAM service with token exchange endpoint
- Litmus API with test suite discovery and execution
- Sentinel web application with CI/CD pipeline
- Database management scripts for all environments
- E2E testing infrastructure
- JWT authentication with tenant isolation

### In Progress 🚧
- **AIGUARDIAN-20**: Ad-hoc test execution feature
  - Frontend components for user-triggered test runs
  - Backend APIs for test suite management
  - Enhanced test execution capabilities

### Planned Features 📝
- AWS Secrets Manager integration for production
- Additional test suite management features
- Enhanced guardrail policies and customization
- Advanced reporting and analytics
- Extended Moonshot integration

## Database Management

### Database Architecture
- **UAM Database**: User accounts, tenants, roles, permissions
- **Litmus Database**: Test suites, results, configurations, reports
- **Sentinel Database**: Guardrail policies, protection rules, audit logs

### Environment Management
- **Local**: Automated setup with Podman containers
- **SIT**: Kubernetes with port forwarding for database access
- **Production**: AWS-managed PostgreSQL with secrets management

### Key Patterns
- Parameterized SQL scripts for environment flexibility
- Prisma migrations for schema management
- Automated seeding for consistent test data
- Clean, initialize, seed workflow for reliable testing

## Security & Compliance

### Government Requirements
- Multi-tenant architecture with data isolation
- Secure credential management (AWS Secrets Manager)
- Audit logging for compliance
- Role-based access control
- Data encryption in transit and at rest

### Authentication Flow
1. User authenticates through UAM service
2. UAM issues JWT token with tenant information
3. Services validate tokens with UAM for each request
4. Permissions checked against user roles and tenant policies

## Deployment Strategy

### Environment Progression
1. **Local Development**: Podman containers with local PostgreSQL
2. **SIT (System Integration Testing)**: Kubernetes with AWS backing
3. **Production**: Full AWS deployment with high availability

### CI/CD Pipeline
- Automated testing on every commit
- Database setup and cleanup scripts
- Container building and registry publishing
- Kubernetes deployment automation
- Post-deployment verification

## Key Success Metrics

### Technical Metrics
- **Uptime**: 99.9% availability across all services
- **Performance**: Sub-200ms API response times
- **Security**: Zero security incidents
- **Testing**: 90%+ code coverage

### Business Metrics
- **Adoption**: Number of government agencies using the platform
- **Testing Coverage**: AI applications tested through Litmus
- **Risk Mitigation**: Security issues identified and prevented
- **Compliance**: Adherence to government AI guidelines

## Future Roadmap

### Phase 1 (Current - Q2 2025)
- Complete ad-hoc test execution feature
- AWS Secrets Manager integration
- Enhanced user interface improvements

### Phase 2 (Q3 2025)
- Advanced analytics and reporting
- Custom guardrail policy creation
- Extended Moonshot evaluation capabilities
- Multi-region deployment support

### Phase 3 (Q4 2025)
- AI model marketplace integration
- Advanced threat detection
- Automated compliance reporting
- Federation with other government AI systems

## Getting Started for New Team Members

### Prerequisites
- Node.js 20.11.1+ and pnpm
- Python 3.11 (for Moonshot components)
- Docker/Podman for local development
- Git with access to the repository

### Setup Instructions
1. Clone the repository
2. Install dependencies: `pnpm install`
3. Set up local databases: `./scripts/postgres/local-setup.sh`
4. Configure environment variables (see `.env.example` files)
5. Start development servers: `pnpm dev`

### Key Resources
- **Memory Bank**: `/memory-bank/` - Project documentation and context
- **Architecture Diagrams**: `/memory-bank/project-architecture.md`
- **API Documentation**: Generated from code and available in development
- **User Guides**: `/apps/aiguardian-web/docs/`

## Support & Contact

For technical questions, architectural decisions, or onboarding support, refer to:
- Memory Bank documentation in `/memory-bank/`
- Service-specific README files
- Inline code documentation
- CI/CD pipeline configurations

---

**Last Updated**: June 2025  
**Version**: Current development branch  
**Maintainer**: AI Guardian Development Team
